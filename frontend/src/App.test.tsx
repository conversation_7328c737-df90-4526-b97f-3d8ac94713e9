/*
 * Copyright 2025 The Kubernetes Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { render, waitFor } from '@testing-library/react';
import React from 'react';
import App from './App';

// todo: enable this after https://github.com/vitest-dev/vitest/issues/4143 is fixed
test.skip('renders without crashing', async () => {
  const { getByText } = render(
    <React.Suspense fallback="Loading...">
      <App />
    </React.Suspense>
  );
  await waitFor(() => {
    expect(getByText(/Skip to main content/i)).toBeInTheDocument();
  });
});
