/*
 * Copyright 2025 The Kubernetes Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { getCluster } from './cluster';
import { setToken } from './auth';
import { testAuth } from './k8s/apiProxy';

export interface LoginRequest {
  username: string;
  password: string;
}

export interface UserInfo {
  username: string;
  serviceAccount: string;
  namespace: string;
  lastLoginAt?: string;
}

export interface LoginResponse {
  token: string;
  expiresAt: string;
  user: UserInfo;
}

export interface AuthStatus {
  enabled: boolean;
  authenticated: boolean;
  user?: UserInfo;
}

/**
 * Authenticate user with username and password
 * @param username - User's username
 * @param password - User's password
 * @returns Promise resolving to HTTP status code
 */
export async function loginWithCredentials(username: string, password: string): Promise<number> {
  try {
    const cluster = getCluster();
    if (!cluster) {
      return 417; // Expectation failed
    }

    const response = await fetch('/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        username: username.trim(),
        password: password,
      }),
    });

    if (response.ok) {
      const data: LoginResponse = await response.json();
      
      // Store the JWT token
      setToken(cluster, data.token);
      
      // Test the authentication to ensure it works
      await testAuth();
      
      return 200;
    } else {
      // Handle different error status codes
      switch (response.status) {
        case 401:
          return 401; // Unauthorized - invalid credentials
        case 403:
          return 403; // Forbidden - user disabled
        case 503:
          return 503; // Service unavailable - auth disabled
        default:
          return response.status;
      }
    }
  } catch (err) {
    console.error('Login error:', err);
    return 500; // Internal server error
  }
}

/**
 * Get authentication status
 * @returns Promise resolving to authentication status
 */
export async function getAuthStatus(): Promise<AuthStatus | null> {
  try {
    const response = await fetch('/api/auth/status', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (response.ok) {
      return await response.json();
    } else {
      console.error('Failed to get auth status:', response.status);
      return null;
    }
  } catch (err) {
    console.error('Auth status error:', err);
    return null;
  }
}

/**
 * Refresh the current JWT token
 * @returns Promise resolving to HTTP status code
 */
export async function refreshToken(): Promise<number> {
  try {
    const cluster = getCluster();
    if (!cluster) {
      return 417;
    }

    const response = await fetch('/api/auth/refresh', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (response.ok) {
      const data: { token: string; expiresAt: string } = await response.json();
      setToken(cluster, data.token);
      return 200;
    } else {
      return response.status;
    }
  } catch (err) {
    console.error('Token refresh error:', err);
    return 500;
  }
}

/**
 * Logout the current user
 * @returns Promise resolving to HTTP status code
 */
export async function logout(): Promise<number> {
  try {
    const response = await fetch('/api/auth/logout', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Clear local token regardless of response
    const cluster = getCluster();
    if (cluster) {
      setToken(cluster, null);
    }

    return response.ok ? 200 : response.status;
  } catch (err) {
    console.error('Logout error:', err);
    
    // Still clear local token on error
    const cluster = getCluster();
    if (cluster) {
      setToken(cluster, null);
    }
    
    return 500;
  }
}

/**
 * Check if user authentication is enabled
 * @returns Promise resolving to boolean indicating if user auth is enabled
 */
export async function isUserAuthEnabled(): Promise<boolean> {
  try {
    const status = await getAuthStatus();
    return status?.enabled ?? false;
  } catch (err) {
    console.error('Error checking user auth status:', err);
    return false;
  }
}

/**
 * Get error message for status code
 * @param statusCode - HTTP status code
 * @returns Human-readable error message
 */
export function getErrorMessage(statusCode: number): string {
  switch (statusCode) {
    case 401:
      return 'Invalid username or password';
    case 403:
      return 'User account is disabled';
    case 417:
      return 'No cluster selected';
    case 503:
      return 'User authentication is disabled';
    case 500:
      return 'Internal server error';
    default:
      return 'Authentication failed';
  }
}
