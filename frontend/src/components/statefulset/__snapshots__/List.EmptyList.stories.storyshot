<body>
  <div>
    <div
      class="MuiBox-root css-j1fy4m"
    >
      <div
        class="MuiGrid-root MuiGrid-container MuiGrid-spacing-xs-2 css-1ts0dnm-MuiGrid-root"
      >
        <div
          class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
        >
          <div
            class="MuiBox-root css-70qvj9"
          >
            <h1
              class="MuiTypography-root MuiTypography-h1 MuiTypography-noWrap css-yeaech-MuiTypography-root"
            >
              Stateful Sets
            </h1>
            <div
              class="MuiBox-root css-ldp2l3"
            >
              <button
                aria-label="Create StatefulSet"
                class="MuiButtonBase-root MuiIconButton-root MuiIconButton-sizeMedium css-whz9ym-MuiButtonBase-root-MuiIconButton-root"
                data-mui-internal-clone-element="true"
                tabindex="0"
                type="button"
              >
                <span
                  class="MuiTouchRipple-root css-8je8zh-MuiTouchRipple-root"
                />
              </button>
            </div>
          </div>
        </div>
        <div
          class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
        >
          <div
            class="MuiGrid-root MuiGrid-container MuiGrid-item css-ztq4zc-MuiGrid-root"
          >
            <div
              class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
            >
              <div
                class="MuiAutocomplete-root MuiAutocomplete-hasPopupIcon css-1x6bjyf-MuiAutocomplete-root"
              >
                <div
                  class="MuiBox-root css-1dipl1t"
                >
                  <div
                    class="MuiFormControl-root MuiFormControl-fullWidth MuiTextField-root css-wb57ya-MuiFormControl-root-MuiTextField-root"
                    style="margin-top: 0px;"
                  >
                    <label
                      class="MuiFormLabel-root MuiInputLabel-root MuiInputLabel-formControl MuiInputLabel-animated MuiInputLabel-shrink MuiInputLabel-sizeSmall MuiInputLabel-outlined MuiFormLabel-colorPrimary MuiInputLabel-root MuiInputLabel-formControl MuiInputLabel-animated MuiInputLabel-shrink MuiInputLabel-sizeSmall MuiInputLabel-outlined css-1f7ywh2-MuiFormLabel-root-MuiInputLabel-root"
                      data-shrink="true"
                      for="namespaces-filter"
                      id="namespaces-filter-label"
                    >
                      Namespaces
                    </label>
                    <div
                      class="MuiInputBase-root MuiOutlinedInput-root MuiInputBase-colorPrimary MuiInputBase-fullWidth MuiInputBase-formControl MuiInputBase-sizeSmall MuiInputBase-adornedEnd MuiAutocomplete-inputRoot css-1xjtaff-MuiInputBase-root-MuiOutlinedInput-root"
                    >
                      <input
                        aria-autocomplete="both"
                        aria-expanded="false"
                        aria-invalid="false"
                        autocapitalize="none"
                        autocomplete="off"
                        class="MuiInputBase-input MuiOutlinedInput-input MuiInputBase-inputSizeSmall MuiInputBase-inputAdornedEnd MuiAutocomplete-input MuiAutocomplete-inputFocused css-19qh8xo-MuiInputBase-input-MuiOutlinedInput-input"
                        id="namespaces-filter"
                        placeholder="Filter"
                        role="combobox"
                        spellcheck="false"
                        type="text"
                        value=""
                      />
                      <div
                        class="MuiAutocomplete-endAdornment css-p1olib-MuiAutocomplete-endAdornment"
                      >
                        <button
                          aria-label="Open"
                          class="MuiButtonBase-root MuiIconButton-root MuiIconButton-sizeMedium MuiAutocomplete-popupIndicator css-1aav1nn-MuiButtonBase-root-MuiIconButton-root-MuiAutocomplete-popupIndicator"
                          tabindex="-1"
                          title="Open"
                          type="button"
                        >
                          <svg
                            aria-hidden="true"
                            class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-i4bv87-MuiSvgIcon-root"
                            data-testid="ArrowDropDownIcon"
                            focusable="false"
                            viewBox="0 0 24 24"
                          >
                            <path
                              d="M7 10l5 5 5-5z"
                            />
                          </svg>
                          <span
                            class="MuiTouchRipple-root css-8je8zh-MuiTouchRipple-root"
                          />
                        </button>
                      </div>
                      <fieldset
                        aria-hidden="true"
                        class="MuiOutlinedInput-notchedOutline css-1d3z3hw-MuiOutlinedInput-notchedOutline"
                      >
                        <legend
                          class="css-14lo706"
                        >
                          <span>
                            Namespaces
                          </span>
                        </legend>
                      </fieldset>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="MuiBox-root css-1txv3mw"
      >
        <div
          class="MuiPaper-root MuiPaper-outlined MuiPaper-rounded css-1guobrs-MuiPaper-root"
        >
          <div
            class="MuiBox-root css-19midj6"
          >
            <p
              class="MuiTypography-root MuiTypography-body1 MuiTypography-alignCenter css-18lkse1-MuiTypography-root"
            >
              No data to be shown.
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</body>