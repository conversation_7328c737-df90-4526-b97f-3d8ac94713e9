<body
  style=""
>
  <div
    class="monaco-aria-container"
  >
    <div
      aria-atomic="true"
      class="monaco-alert"
      role="alert"
    />
    <div
      aria-atomic="true"
      class="monaco-alert"
      role="alert"
    />
    <div
      aria-atomic="true"
      aria-live="polite"
      class="monaco-status"
      role="complementary"
    />
    <div
      aria-atomic="true"
      aria-live="polite"
      class="monaco-status"
      role="complementary"
    />
  </div>
  <div>
    <div
      class="MuiGrid-root MuiGrid-container MuiGrid-spacing-xs-1 css-bsyb48-MuiGrid-root"
    >
      <div
        class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 css-49904w-MuiGrid-root"
      >
        <div
          class="MuiBox-root css-p0cik4"
        >
          <button
            class="MuiButtonBase-root MuiButton-root MuiButton-text MuiButton-textPrimary MuiButton-sizeSmall MuiButton-textSizeSmall MuiButton-colorPrimary MuiButton-root MuiButton-text MuiButton-textPrimary MuiButton-sizeSmall MuiButton-textSizeSmall MuiButton-colorPrimary css-w7fpvq-MuiButtonBase-root-MuiButton-root"
            tabindex="0"
            type="button"
          >
            <span
              class="MuiButton-icon MuiButton-startIcon MuiButton-iconSizeSmall css-y6rp3m-MuiButton-startIcon"
            />
            <p
              class="MuiTypography-root MuiTypography-body1 css-1ezega9-MuiTypography-root"
              style="padding-top: 3px;"
            >
              Back
            </p>
            <span
              class="MuiTouchRipple-root css-8je8zh-MuiTouchRipple-root"
            />
          </button>
        </div>
      </div>
      <div
        class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 css-49904w-MuiGrid-root"
      >
        <div
          class="MuiBox-root css-p0cik4"
        >
          <div
            class="MuiGrid-root MuiGrid-container MuiGrid-spacing-xs-2 css-1qm9nul-MuiGrid-root"
          >
            <div
              class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
            />
            <div
              class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
            >
              <div
                class="MuiGrid-root MuiGrid-container MuiGrid-item css-43x3z6-MuiGrid-root"
              >
                <div
                  class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                />
                <div
                  class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                />
                <div
                  class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                />
                <div
                  class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 css-49904w-MuiGrid-root"
      >
        <div
          class="MuiBox-root css-p0cik4"
        >
          <div
            class="MuiBox-root css-5cned0"
          >
            <span
              class="MuiCircularProgress-root MuiCircularProgress-indeterminate MuiCircularProgress-colorPrimary css-wdedfu-MuiCircularProgress-root"
              role="progressbar"
              style="width: 40px; height: 40px;"
              title="Loading resource data"
            >
              <svg
                class="MuiCircularProgress-svg css-1idz92c-MuiCircularProgress-svg"
                viewBox="22 22 44 44"
              >
                <circle
                  class="MuiCircularProgress-circle MuiCircularProgress-circleIndeterminate css-176wh8e-MuiCircularProgress-circle"
                  cx="44"
                  cy="44"
                  fill="none"
                  r="20.2"
                  stroke-width="3.6"
                />
              </svg>
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</body>