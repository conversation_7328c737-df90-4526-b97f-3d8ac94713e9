/*
 * Copyright 2025 The Kubernetes Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { describe, expect, it } from 'vitest';
import { searchWithQuery } from './searchWithQuery';

describe('searchWithQuery', () => {
  const items = [
    { id: 1, jsonData: { name: '<PERSON>', age: 30, city: 'New York' } },
    { id: 2, jsonData: { name: '<PERSON>', age: 25, city: 'Los Angeles' } },
    { id: 3, jsonData: { name: '<PERSON>', age: 35, city: 'New York' } },
    { id: 4, jsonData: { name: '<PERSON>', age: 30 } }, // Missing city
  ];

  it('should return items matching the query', async () => {
    const query = 'age > 28';
    const interruptRef = { current: false };
    const { results, searchTimeMs } = await searchWithQuery(items, query, interruptRef);

    expect(results).toEqual([items[0], items[2], items[3]]);
    expect(searchTimeMs).toBeGreaterThanOrEqual(0);
  });

  it('should handle complex queries involving multiple keys', async () => {
    const query = `city === 'New York' && age >= 35`;
    const interruptRef = { current: false };
    const { results } = await searchWithQuery(items, query, interruptRef);

    expect(results).toEqual([items[2]]);
  });

  it('should return an empty array if no items match', async () => {
    const query = 'age > 40';
    const interruptRef = { current: false };
    const { results } = await searchWithQuery(items, query, interruptRef);

    expect(results).toEqual([]);
  });

  it('should return all items if query is just "true"', async () => {
    const query = 'true';
    const interruptRef = { current: false };
    const { results } = await searchWithQuery(items, query, interruptRef);

    expect(results).toEqual(items);
  });

  it('should return an empty array for empty input items', async () => {
    const query = 'age > 20';
    const interruptRef = { current: false };
    const { results, searchTimeMs } = await searchWithQuery([], query, interruptRef);

    expect(results).toEqual([]);
    expect(searchTimeMs).toBeGreaterThanOrEqual(0); // Should be very small
  });

  it('should return empty results and 0 time if the query string is syntactically invalid', async () => {
    const query = 'age > ??? // invalid syntax';
    const interruptRef = { current: false };
    const { results, searchTimeMs } = await searchWithQuery(items, query, interruptRef);

    // eval fails, queryFn is undefined
    expect(results).toEqual([]);
    expect(searchTimeMs).toBe(0);
  });

  it('should gracefully handle runtime errors within the query logic (e.g., accessing undefined property)', async () => {
    // This query will fail on item 4 where 'city' is undefined
    const query = 'city.toLowerCase() === "new york"';
    const interruptRef = { current: false };
    const { results } = await searchWithQuery(items, query, interruptRef);

    // Should only return item 1 and 3 where the query executed without error and returned true
    // It skips item 4 due to the runtime error caught internally
    expect(results).toEqual([items[0], items[2]]);
  });

  it('should stop processing and return partial results if interruptRef is set to true', async () => {
    // NOTE: Testing interruption precisely depends on event loop timing and batch size.
    // With a small item list and batchSize=5000, interruption only happens *after* the loop.
    // This test primarily ensures the check exists and *might* stop early under load/different timing.
    // It's hard to guarantee partial results without > batchSize items or mocking timers.
    const largeItems = Array.from({ length: 6 }, (_, i) => ({
      // Still < batchSize
      id: i,
      jsonData: { value: i },
    }));
    const query = 'value < 10'; // Should match all if not interrupted
    const interruptRef = { current: true }; // Interrupt immediately

    const { results } = await searchWithQuery(largeItems, query, interruptRef);

    // Expect fewer results than total, potentially empty if interrupted before first check after yield.
    // Because the check is *after* the yield, and yield only happens at the end for lists < batchSize,
    // it's likely *all* items are processed before the check.
    // A more robust test might need > batchSize items or different function structure.
    // For this simple test, we check if it didn't return *all* items, implying the break *could* work.
    // Expecting empty results is the most direct check that the break happened *very* early.
    expect(results.length).toBe(0); // Assumes break happens effectively before any results pushed post-yield
  });

  it('should work correctly if item jsonData properties differ', async () => {
    const mixedItems = [
      { id: 1, jsonData: { type: 'A', value: 10 } },
      { id: 2, jsonData: { category: 'B', count: 5 } },
      { id: 3, jsonData: { type: 'A', count: 20 } },
    ];
    // getTopLevelKeys should correctly identify 'type', 'value', 'category', 'count'
    const query = `type === 'A' || count > 10`;
    const interruptRef = { current: false };
    const { results } = await searchWithQuery(mixedItems, query, interruptRef);

    // Item 1 matches type === 'A'
    // Item 2 doesn't match
    // Item 3 matches count > 10 (and type === 'A')
    expect(results).toEqual([mixedItems[0], mixedItems[2]]);
  });
});
