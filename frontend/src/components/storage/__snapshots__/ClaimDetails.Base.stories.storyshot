<body>
  <div>
    <div
      class="MuiGrid-root MuiGrid-container MuiGrid-spacing-xs-1 css-bsyb48-MuiGrid-root"
    >
      <div
        class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 css-49904w-MuiGrid-root"
      >
        <div
          class="MuiBox-root css-p0cik4"
        >
          <button
            class="MuiButtonBase-root MuiButton-root MuiButton-text Mu<PERSON><PERSON><PERSON>on-textPrimary MuiButton-sizeSmall MuiButton-textSizeSmall MuiButton-colorPrimary MuiButton-disableElevation MuiButton-root MuiButton-text MuiButton-textPrimary MuiButton-sizeSmall MuiButton-textSizeSmall MuiButton-colorPrimary MuiButton-disableElevation css-1j11y9k-MuiButtonBase-root-MuiButton-root"
            tabindex="0"
            type="button"
          >
            <span
              class="MuiButton-icon MuiButton-startIcon MuiButton-iconSizeSmall css-y6rp3m-MuiButton-startIcon"
            />
            <p
              class="MuiTypography-root MuiTypography-body1 css-1ezega9-MuiTypography-root"
              style="padding-top: 3px;"
            >
              Back
            </p>
            <span
              class="MuiTouchRipple-root css-8je8zh-MuiTouchRipple-root"
            />
          </button>
        </div>
      </div>
      <div
        class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 css-49904w-MuiGrid-root"
      >
        <div
          class="MuiBox-root css-p0cik4"
        >
          <div
            class="MuiGrid-root MuiGrid-container MuiGrid-spacing-xs-2 css-1ts0dnm-MuiGrid-root"
          >
            <div
              class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
            >
              <div
                class="MuiBox-root css-70qvj9"
              >
                <h1
                  class="MuiTypography-root MuiTypography-h1 MuiTypography-noWrap css-yeaech-MuiTypography-root"
                >
                  PersistentVolumeClaim: my-pvc
                </h1>
                <div
                  class="MuiBox-root css-ldp2l3"
                />
              </div>
            </div>
            <div
              class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
            >
              <div
                class="MuiGrid-root MuiGrid-container MuiGrid-item css-ztq4zc-MuiGrid-root"
              >
                <div
                  class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                />
                <div
                  class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                />
                <div
                  class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                >
                  <button
                    aria-label="Edit"
                    class="MuiButtonBase-root MuiIconButton-root MuiIconButton-sizeMedium css-whz9ym-MuiButtonBase-root-MuiIconButton-root"
                    data-mui-internal-clone-element="true"
                    tabindex="0"
                    type="button"
                  >
                    <span
                      class="MuiTouchRipple-root css-8je8zh-MuiTouchRipple-root"
                    />
                  </button>
                </div>
                <div
                  class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                >
                  <button
                    aria-label="Delete"
                    class="MuiButtonBase-root MuiIconButton-root MuiIconButton-sizeMedium css-whz9ym-MuiButtonBase-root-MuiIconButton-root"
                    data-mui-internal-clone-element="true"
                    tabindex="0"
                    type="button"
                  >
                    <span
                      class="MuiTouchRipple-root css-8je8zh-MuiTouchRipple-root"
                    />
                  </button>
                  <div />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 css-49904w-MuiGrid-root"
      >
        <div
          class="MuiBox-root css-p0cik4"
        >
          <div
            class="MuiBox-root css-j1fy4m"
          >
            <div
              aria-busy="false"
              aria-live="polite"
              class="MuiBox-root css-1txv3mw"
            >
              <div
                class="MuiBox-root css-0"
              >
                <dl
                  class="MuiGrid-root MuiGrid-container css-kxuems-MuiGrid-root"
                >
                  <dt
                    class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-4 css-1iczkge-MuiGrid-root"
                  >
                    Name
                  </dt>
                  <dd
                    class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-8 css-deb4a-MuiGrid-root"
                  >
                    <span
                      class="MuiTypography-root MuiTypography-body1 css-e06lsu-MuiTypography-root"
                    >
                      my-pvc
                    </span>
                  </dd>
                  <dt
                    class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-4 css-1iczkge-MuiGrid-root"
                  >
                    Namespace
                  </dt>
                  <dd
                    class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-8 css-deb4a-MuiGrid-root"
                  >
                    <a
                      class="MuiTypography-root MuiTypography-inherit MuiLink-root MuiLink-underlineHover css-1w0w5yv-MuiTypography-root-MuiLink-root"
                      href="/"
                    >
                      default
                    </a>
                  </dd>
                  <dt
                    class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-4 css-1iczkge-MuiGrid-root"
                  >
                    Creation
                  </dt>
                  <dd
                    class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-8 css-deb4a-MuiGrid-root"
                  >
                    <span
                      class="MuiTypography-root MuiTypography-body1 css-e06lsu-MuiTypography-root"
                    >
                      2023-04-27T20:31:27.000Z
                    </span>
                  </dd>
                  <dt
                    class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-4 css-1iczkge-MuiGrid-root"
                  >
                    Status
                  </dt>
                  <dd
                    class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-8 css-deb4a-MuiGrid-root"
                  >
                    <span
                      class="MuiTypography-root MuiTypography-body1 css-1ybpqju-MuiTypography-root"
                    >
                      Bound
                    </span>
                  </dd>
                  <dt
                    class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-4 css-1iczkge-MuiGrid-root"
                  >
                    Capacity
                  </dt>
                  <dd
                    class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-8 css-deb4a-MuiGrid-root"
                  >
                    <span
                      class="MuiTypography-root MuiTypography-body1 css-e06lsu-MuiTypography-root"
                    >
                      8Gi
                    </span>
                  </dd>
                  <dt
                    class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-4 css-1iczkge-MuiGrid-root"
                  >
                    Access Modes
                  </dt>
                  <dd
                    class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-8 css-deb4a-MuiGrid-root"
                  >
                    <span
                      class="MuiTypography-root MuiTypography-body1 css-e06lsu-MuiTypography-root"
                    >
                      ReadWriteOnce
                    </span>
                  </dd>
                  <dt
                    class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-4 css-1iczkge-MuiGrid-root"
                  >
                    Volume Mode
                  </dt>
                  <dd
                    class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-8 css-deb4a-MuiGrid-root"
                  >
                    <span
                      class="MuiTypography-root MuiTypography-body1 css-e06lsu-MuiTypography-root"
                    >
                      Filesystem
                    </span>
                  </dd>
                  <dt
                    class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-4 css-iqixpy-MuiGrid-root"
                  >
                    Storage Class
                  </dt>
                  <dd
                    class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-8 css-1xrovmc-MuiGrid-root"
                  >
                    <a
                      aria-label="default"
                      class="MuiTypography-root MuiTypography-inherit MuiLink-root MuiLink-underlineHover css-1w0w5yv-MuiTypography-root-MuiLink-root"
                      data-mui-internal-clone-element="true"
                      href="/"
                    >
                      default
                    </a>
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 css-49904w-MuiGrid-root"
      >
        <div
          class="MuiBox-root css-p0cik4"
        >
          <div
            class="MuiBox-root css-j1fy4m"
          >
            <div
              class="MuiGrid-root MuiGrid-container MuiGrid-spacing-xs-2 css-1ts0dnm-MuiGrid-root"
            >
              <div
                class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
              >
                <div
                  class="MuiBox-root css-70qvj9"
                >
                  <h2
                    class="MuiTypography-root MuiTypography-h2 MuiTypography-noWrap css-m5vcfd-MuiTypography-root"
                  >
                    Events
                  </h2>
                  <div
                    class="MuiBox-root css-ldp2l3"
                  />
                </div>
              </div>
            </div>
            <div
              class="MuiBox-root css-1txv3mw"
            >
              <div
                class="MuiPaper-root MuiPaper-outlined MuiPaper-rounded css-1guobrs-MuiPaper-root"
              >
                <div
                  class="MuiBox-root css-19midj6"
                >
                  <p
                    class="MuiTypography-root MuiTypography-body1 MuiTypography-alignCenter css-18lkse1-MuiTypography-root"
                  >
                    No data to be shown.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</body>