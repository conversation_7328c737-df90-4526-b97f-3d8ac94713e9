<body>
  <div>
    <div
      class="MuiContainer-root MuiContainer-maxWidthXl css-4hqp1a-MuiContainer-root"
    >
      <div
        class="MuiGrid-root MuiGrid-container MuiGrid-spacing-xs-1 css-1ub3t8b-MuiGrid-root"
      >
        <div
          class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 css-49904w-MuiGrid-root"
        >
          <div
            class="MuiBox-root css-p0cik4"
          >
            <div
              class="MuiBox-root css-j1fy4m"
            >
              <div
                class="MuiGrid-root MuiGrid-container MuiGrid-spacing-xs-2 css-1ts0dnm-MuiGrid-root"
              >
                <div
                  class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                >
                  <div
                    class="MuiBox-root css-70qvj9"
                  >
                    <h2
                      class="MuiTypography-root MuiTypography-h2 MuiTypography-noWrap css-m5vcfd-MuiTypography-root"
                    >
                      Overview
                    </h2>
                    <div
                      class="MuiBox-root css-ldp2l3"
                    />
                  </div>
                </div>
              </div>
              <div
                class="MuiBox-root css-lpikod"
              >
                <div
                  class="MuiGrid-root MuiGrid-container MuiGrid-spacing-xs-4 css-13cu5dn-MuiGrid-root"
                >
                  <div
                    class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-true css-18ph6tx-MuiGrid-root"
                  >
                    <div
                      class="MuiPaper-root MuiPaper-outlined MuiPaper-rounded css-1oobngp-MuiPaper-root"
                    >
                      <div
                        class="MuiBox-root css-qrw4x1"
                      >
                        <div
                          class="MuiBox-root css-1sl8dhm"
                        >
                          <div
                            class="MuiBox-root css-0"
                          >
                            <p
                              class="MuiTypography-root MuiTypography-body1 MuiTypography-gutterBottom css-16da70x-MuiTypography-root"
                            >
                              CPU Usage
                            </p>
                          </div>
                          <p
                            class="MuiTypography-root MuiTypography-body1 MuiTypography-gutterBottom css-7142su-MuiTypography-root"
                          />
                        </div>
                        <div
                          class="MuiBox-root css-0"
                        >
                          <div
                            aria-busy="false"
                            aria-live="polite"
                            class="MuiBox-root css-2esbmj"
                          >
                            <div
                              class="recharts-wrapper"
                              style="position: relative; cursor: default; width: 112px; height: 112px; margin-left: auto; margin-right: auto;"
                            >
                              <svg
                                class="recharts-surface"
                                cx="70"
                                cy="70"
                                height="112"
                                style="width: 100%; height: 100%;"
                                viewBox="0 0 112 112"
                                width="112"
                              >
                                <title />
                                <desc />
                                <defs>
                                  <clippath
                                    id="recharts-id"
                                  >
                                    <rect
                                      height="102"
                                      width="102"
                                      x="5"
                                      y="5"
                                    />
                                  </clippath>
                                </defs>
                                <g
                                  class="recharts-layer recharts-pie"
                                  tabindex="0"
                                >
                                  <g
                                    class="recharts-layer recharts-pie-sector"
                                    tabindex="-1"
                                  />
                                  <g
                                    class="recharts-layer recharts-pie-sector"
                                    tabindex="-1"
                                  >
                                    <path
                                      class="recharts-sector"
                                      cx="61"
                                      cy="61"
                                      d="M 61,16.199999999999996
    A 44.800000000000004,44.800000000000004,0,
    1,1,
    60.99921809249515,16.20000000682343
  L 60.999410078712856,27.200000005148027
            A 33.800000000000004,33.800000000000004,0,
            1,0,
            61,27.199999999999996 Z"
                                      fill="#e0e0e0"
                                      name="total"
                                      role="img"
                                      stroke="#e0e0e0"
                                      tabindex="-1"
                                    />
                                  </g>
                                  <text
                                    class="recharts-text recharts-label"
                                    fill="#808080"
                                    offset="5"
                                    style="font-size: 16.8px; fill: #000;"
                                    text-anchor="middle"
                                    x="61"
                                    y="61"
                                  >
                                    <tspan
                                      dy="0.355em"
                                      x="61"
                                    >
                                      …
                                    </tspan>
                                  </text>
                                </g>
                              </svg>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div
                    class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-true css-18ph6tx-MuiGrid-root"
                  >
                    <div
                      class="MuiPaper-root MuiPaper-outlined MuiPaper-rounded css-1oobngp-MuiPaper-root"
                    >
                      <div
                        class="MuiBox-root css-qrw4x1"
                      >
                        <div
                          class="MuiBox-root css-1sl8dhm"
                        >
                          <div
                            class="MuiBox-root css-0"
                          >
                            <p
                              class="MuiTypography-root MuiTypography-body1 MuiTypography-gutterBottom css-16da70x-MuiTypography-root"
                            >
                              Memory Usage
                            </p>
                          </div>
                          <p
                            class="MuiTypography-root MuiTypography-body1 MuiTypography-gutterBottom css-7142su-MuiTypography-root"
                          />
                        </div>
                        <div
                          class="MuiBox-root css-0"
                        >
                          <div
                            aria-busy="false"
                            aria-live="polite"
                            class="MuiBox-root css-2esbmj"
                          >
                            <div
                              class="recharts-wrapper"
                              style="position: relative; cursor: default; width: 112px; height: 112px; margin-left: auto; margin-right: auto;"
                            >
                              <svg
                                class="recharts-surface"
                                cx="70"
                                cy="70"
                                height="112"
                                style="width: 100%; height: 100%;"
                                viewBox="0 0 112 112"
                                width="112"
                              >
                                <title />
                                <desc />
                                <defs>
                                  <clippath
                                    id="recharts-id"
                                  >
                                    <rect
                                      height="102"
                                      width="102"
                                      x="5"
                                      y="5"
                                    />
                                  </clippath>
                                </defs>
                                <g
                                  class="recharts-layer recharts-pie"
                                  tabindex="0"
                                >
                                  <g
                                    class="recharts-layer recharts-pie-sector"
                                    tabindex="-1"
                                  />
                                  <g
                                    class="recharts-layer recharts-pie-sector"
                                    tabindex="-1"
                                  >
                                    <path
                                      class="recharts-sector"
                                      cx="61"
                                      cy="61"
                                      d="M 61,16.199999999999996
    A 44.800000000000004,44.800000000000004,0,
    1,1,
    60.99921809249515,16.20000000682343
  L 60.999410078712856,27.200000005148027
            A 33.800000000000004,33.800000000000004,0,
            1,0,
            61,27.199999999999996 Z"
                                      fill="#e0e0e0"
                                      name="total"
                                      role="img"
                                      stroke="#e0e0e0"
                                      tabindex="-1"
                                    />
                                  </g>
                                  <text
                                    class="recharts-text recharts-label"
                                    fill="#808080"
                                    offset="5"
                                    style="font-size: 16.8px; fill: #000;"
                                    text-anchor="middle"
                                    x="61"
                                    y="61"
                                  >
                                    <tspan
                                      dy="0.355em"
                                      x="61"
                                    >
                                      …
                                    </tspan>
                                  </text>
                                </g>
                              </svg>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div
                    class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-true css-18ph6tx-MuiGrid-root"
                  >
                    <div
                      class="MuiPaper-root MuiPaper-outlined MuiPaper-rounded css-1oobngp-MuiPaper-root"
                    >
                      <div
                        class="MuiBox-root css-qrw4x1"
                      >
                        <div
                          class="MuiBox-root css-1sl8dhm"
                        >
                          <div
                            class="MuiBox-root css-0"
                          >
                            <p
                              class="MuiTypography-root MuiTypography-body1 MuiTypography-gutterBottom css-16da70x-MuiTypography-root"
                            >
                              Pods
                            </p>
                          </div>
                          <p
                            class="MuiTypography-root MuiTypography-body1 MuiTypography-gutterBottom css-7142su-MuiTypography-root"
                          >
                            0 / 0 Requested
                          </p>
                        </div>
                        <div
                          class="MuiBox-root css-0"
                        >
                          <div
                            aria-busy="false"
                            aria-live="polite"
                            class="MuiBox-root css-2esbmj"
                          >
                            <div
                              class="recharts-wrapper"
                              style="position: relative; cursor: default; width: 112px; height: 112px; margin-left: auto; margin-right: auto;"
                            >
                              <svg
                                class="recharts-surface"
                                cx="70"
                                cy="70"
                                height="112"
                                style="width: 100%; height: 100%;"
                                viewBox="0 0 112 112"
                                width="112"
                              >
                                <title />
                                <desc />
                                <defs>
                                  <clippath
                                    id="recharts-id"
                                  >
                                    <rect
                                      height="102"
                                      width="102"
                                      x="5"
                                      y="5"
                                    />
                                  </clippath>
                                </defs>
                                <g
                                  class="recharts-layer recharts-pie"
                                  tabindex="0"
                                >
                                  <g
                                    class="recharts-layer recharts-pie-sector"
                                    tabindex="-1"
                                  />
                                  <g
                                    class="recharts-layer recharts-pie-sector"
                                    tabindex="-1"
                                  />
                                  <g
                                    class="recharts-layer recharts-pie-sector"
                                    tabindex="-1"
                                  >
                                    <path
                                      class="recharts-sector"
                                      cx="61"
                                      cy="61"
                                      d="M 61,16.199999999999996
    A 44.800000000000004,44.800000000000004,0,
    1,1,
    60.99921809249515,16.20000000682343
  L 60.999410078712856,27.200000005148027
            A 33.800000000000004,33.800000000000004,0,
            1,0,
            61,27.199999999999996 Z"
                                      fill="#e0e0e0"
                                      name="total"
                                      role="img"
                                      stroke="#e0e0e0"
                                      tabindex="-1"
                                    />
                                  </g>
                                  <text
                                    class="recharts-text recharts-label"
                                    fill="#808080"
                                    offset="5"
                                    style="font-size: 16.8px; fill: #000;"
                                    text-anchor="middle"
                                    x="61"
                                    y="61"
                                  >
                                    <tspan
                                      dy="0.355em"
                                      x="61"
                                    >
                                      0 %
                                    </tspan>
                                  </text>
                                </g>
                              </svg>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div
                    class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-true css-18ph6tx-MuiGrid-root"
                  >
                    <div
                      class="MuiPaper-root MuiPaper-outlined MuiPaper-rounded css-1oobngp-MuiPaper-root"
                    >
                      <div
                        class="MuiBox-root css-qrw4x1"
                      >
                        <div
                          class="MuiBox-root css-1sl8dhm"
                        >
                          <div
                            class="MuiBox-root css-0"
                          >
                            <p
                              class="MuiTypography-root MuiTypography-body1 MuiTypography-gutterBottom css-16da70x-MuiTypography-root"
                            >
                              Nodes
                            </p>
                          </div>
                          <p
                            class="MuiTypography-root MuiTypography-body1 MuiTypography-gutterBottom css-7142su-MuiTypography-root"
                          >
                            0 / 2 Ready
                          </p>
                        </div>
                        <div
                          class="MuiBox-root css-0"
                        >
                          <div
                            aria-busy="false"
                            aria-live="polite"
                            class="MuiBox-root css-2esbmj"
                          >
                            <div
                              class="recharts-wrapper"
                              style="position: relative; cursor: default; width: 112px; height: 112px; margin-left: auto; margin-right: auto;"
                            >
                              <svg
                                class="recharts-surface"
                                cx="70"
                                cy="70"
                                height="112"
                                style="width: 100%; height: 100%;"
                                viewBox="0 0 112 112"
                                width="112"
                              >
                                <title />
                                <desc />
                                <defs>
                                  <clippath
                                    id="recharts-id"
                                  >
                                    <rect
                                      height="102"
                                      width="102"
                                      x="5"
                                      y="5"
                                    />
                                  </clippath>
                                </defs>
                                <g
                                  class="recharts-layer recharts-pie"
                                  tabindex="0"
                                >
                                  <g
                                    class="recharts-layer recharts-pie-sector"
                                    tabindex="-1"
                                  />
                                  <g
                                    class="recharts-layer recharts-pie-sector"
                                    tabindex="-1"
                                  >
                                    <path
                                      class="recharts-sector"
                                      cx="61"
                                      cy="61"
                                      d="M 61,16.199999999999996
    A 44.800000000000004,44.800000000000004,0,
    1,1,
    60.99921809249515,16.20000000682343
  L 60.999410078712856,27.200000005148027
            A 33.800000000000004,33.800000000000004,0,
            1,0,
            61,27.199999999999996 Z"
                                      fill="#c62828"
                                      name="notReady"
                                      role="img"
                                      stroke="#e0e0e0"
                                      tabindex="-1"
                                    />
                                  </g>
                                  <g
                                    class="recharts-layer recharts-pie-sector"
                                    tabindex="-1"
                                  />
                                  <text
                                    class="recharts-text recharts-label"
                                    fill="#808080"
                                    offset="5"
                                    style="font-size: 16.8px; fill: #000;"
                                    text-anchor="middle"
                                    x="61"
                                    y="61"
                                  >
                                    <tspan
                                      dy="0.355em"
                                      x="61"
                                    >
                                      0.0 %
                                    </tspan>
                                  </text>
                                </g>
                              </svg>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 css-49904w-MuiGrid-root"
        >
          <div
            class="MuiBox-root css-p0cik4"
          >
            <div
              class="MuiBox-root css-j1fy4m"
            >
              <div
                class="MuiGrid-root MuiGrid-container MuiGrid-spacing-xs-2 css-1ts0dnm-MuiGrid-root"
              >
                <div
                  class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                >
                  <div
                    class="MuiBox-root css-70qvj9"
                  >
                    <h1
                      class="MuiTypography-root MuiTypography-h1 MuiTypography-noWrap css-yeaech-MuiTypography-root"
                    >
                      Events
                    </h1>
                    <div
                      class="MuiBox-root css-ldp2l3"
                    >
                      <label
                        class="MuiFormControlLabel-root MuiFormControlLabel-labelPlacementEnd css-j204z7-MuiFormControlLabel-root"
                      >
                        <span
                          class="MuiSwitch-root MuiSwitch-sizeMedium css-julti5-MuiSwitch-root"
                        >
                          <span
                            class="MuiButtonBase-root MuiSwitch-switchBase MuiSwitch-colorPrimary Mui-checked PrivateSwitchBase-root MuiSwitch-switchBase MuiSwitch-colorPrimary Mui-checked Mui-checked css-1emuodk-MuiButtonBase-root-MuiSwitch-switchBase"
                          >
                            <input
                              checked=""
                              class="PrivateSwitchBase-input MuiSwitch-input css-1m9pwf3"
                              type="checkbox"
                            />
                            <span
                              class="MuiSwitch-thumb css-jsexje-MuiSwitch-thumb"
                            />
                            <span
                              class="MuiTouchRipple-root css-8je8zh-MuiTouchRipple-root"
                            />
                          </span>
                          <span
                            class="MuiSwitch-track css-1yjjitx-MuiSwitch-track"
                          />
                        </span>
                        <span
                          class="MuiTypography-root MuiTypography-body1 MuiFormControlLabel-label css-1ezega9-MuiTypography-root"
                        >
                          Only warnings (0)
                        </span>
                      </label>
                    </div>
                  </div>
                </div>
                <div
                  class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                >
                  <div
                    class="MuiGrid-root MuiGrid-container MuiGrid-item css-ztq4zc-MuiGrid-root"
                  >
                    <div
                      class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                    >
                      <div
                        class="MuiAutocomplete-root MuiAutocomplete-hasPopupIcon css-1x6bjyf-MuiAutocomplete-root"
                      >
                        <div
                          class="MuiBox-root css-1dipl1t"
                        >
                          <div
                            class="MuiFormControl-root MuiFormControl-fullWidth MuiTextField-root css-wb57ya-MuiFormControl-root-MuiTextField-root"
                            style="margin-top: 0px;"
                          >
                            <label
                              class="MuiFormLabel-root MuiInputLabel-root MuiInputLabel-formControl MuiInputLabel-animated MuiInputLabel-shrink MuiInputLabel-sizeSmall MuiInputLabel-outlined MuiFormLabel-colorPrimary MuiInputLabel-root MuiInputLabel-formControl MuiInputLabel-animated MuiInputLabel-shrink MuiInputLabel-sizeSmall MuiInputLabel-outlined css-1f7ywh2-MuiFormLabel-root-MuiInputLabel-root"
                              data-shrink="true"
                              for="namespaces-filter"
                              id="namespaces-filter-label"
                            >
                              Namespaces
                            </label>
                            <div
                              class="MuiInputBase-root MuiOutlinedInput-root MuiInputBase-colorPrimary MuiInputBase-fullWidth MuiInputBase-formControl MuiInputBase-sizeSmall MuiInputBase-adornedEnd MuiAutocomplete-inputRoot css-1xjtaff-MuiInputBase-root-MuiOutlinedInput-root"
                            >
                              <input
                                aria-autocomplete="both"
                                aria-expanded="false"
                                aria-invalid="false"
                                autocapitalize="none"
                                autocomplete="off"
                                class="MuiInputBase-input MuiOutlinedInput-input MuiInputBase-inputSizeSmall MuiInputBase-inputAdornedEnd MuiAutocomplete-input MuiAutocomplete-inputFocused css-19qh8xo-MuiInputBase-input-MuiOutlinedInput-input"
                                id="namespaces-filter"
                                placeholder="Filter"
                                role="combobox"
                                spellcheck="false"
                                type="text"
                                value=""
                              />
                              <div
                                class="MuiAutocomplete-endAdornment css-p1olib-MuiAutocomplete-endAdornment"
                              >
                                <button
                                  aria-label="Open"
                                  class="MuiButtonBase-root MuiIconButton-root MuiIconButton-sizeMedium MuiAutocomplete-popupIndicator css-1aav1nn-MuiButtonBase-root-MuiIconButton-root-MuiAutocomplete-popupIndicator"
                                  tabindex="-1"
                                  title="Open"
                                  type="button"
                                >
                                  <svg
                                    aria-hidden="true"
                                    class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-i4bv87-MuiSvgIcon-root"
                                    data-testid="ArrowDropDownIcon"
                                    focusable="false"
                                    viewBox="0 0 24 24"
                                  >
                                    <path
                                      d="M7 10l5 5 5-5z"
                                    />
                                  </svg>
                                  <span
                                    class="MuiTouchRipple-root css-8je8zh-MuiTouchRipple-root"
                                  />
                                </button>
                              </div>
                              <fieldset
                                aria-hidden="true"
                                class="MuiOutlinedInput-notchedOutline css-1d3z3hw-MuiOutlinedInput-notchedOutline"
                              >
                                <legend
                                  class="css-14lo706"
                                >
                                  <span>
                                    Namespaces
                                  </span>
                                </legend>
                              </fieldset>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="MuiBox-root css-1txv3mw"
              >
                <div
                  class="MuiPaper-root MuiPaper-outlined MuiPaper-rounded css-1guobrs-MuiPaper-root"
                >
                  <div
                    class="MuiBox-root css-19midj6"
                  >
                    <p
                      class="MuiTypography-root MuiTypography-body1 MuiTypography-alignCenter css-18lkse1-MuiTypography-root"
                    >
                      No data to be shown.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</body>