<body>
  <div
    aria-hidden="true"
  >
    <main
      class="MuiBox-root css-0"
    />
  </div>
  <div
    aria-busy="false"
    class="MuiDialog-root MuiModal-root css-zw3mfo-MuiModal-root-MuiDialog-root"
    role="presentation"
  >
    <div
      aria-hidden="true"
      class="MuiBackdrop-root MuiModal-backdrop css-yiavyu-MuiBackdrop-root-MuiDialog-backdrop"
      style="opacity: 1; webkit-transition: opacity 225ms cubic-bezier(0.4, 0, 0.2, 1) 0ms; transition: opacity 225ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;"
    />
    <div
      data-testid="sentinelStart"
      tabindex="0"
    />
    <div
      class="MuiDialog-container MuiDialog-scrollPaper css-hz1bth-MuiDialog-container"
      role="presentation"
      style="opacity: 1; webkit-transition: opacity 225ms cubic-bezier(0.4, 0, 0.2, 1) 0ms; transition: opacity 225ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;"
      tabindex="-1"
    >
      <div
        aria-labelledby="chooser-dialog-title"
        class="MuiPaper-root MuiPaper-outlined MuiPaper-rounded MuiDialog-paper MuiDialog-paperScrollPaper MuiDialog-paperWidthSm css-1708vl9-MuiPaper-root-MuiDialog-paper"
        role="dialog"
      >
        <h2
          class="MuiTypography-root MuiTypography-h6 MuiDialogTitle-root css-wlr4ab-MuiTypography-root-MuiDialogTitle-root"
          id="chooser-dialog-title"
          style="display: flex;"
        >
          <div
            class="MuiGrid-root MuiGrid-container css-9cyib4-MuiGrid-root"
          >
            <div
              class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
            >
              <h1
                class="MuiTypography-root MuiTypography-h1 css-1kazmbo-MuiTypography-root"
                style="font-size: 1.25rem; font-weight: 500; line-height: 1.6;"
              >
                <svg
                  fill="none"
                  height="38"
                  style="width: auto; height: 32px;"
                  viewBox="0 0 184 38"
                  width="184"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M15.0001 26.1066C18.7118 26.1066 21.7299 23.0885 21.7299 19.3768C21.7299 15.6651 18.7118 12.647 15.0001 12.647C11.2884 12.647 8.27026 15.6463 8.27026 19.3768C8.27026 23.1072 11.2696 26.1066 15.0001 26.1066Z"
                    fill="#FFF200"
                  />
                  <path
                    clip-rule="evenodd"
                    d="M30 23.8326L27.6587 26.9808L30 34.2372L15.0104 37.5524L0 34.2372L2.34129 26.9808L0 23.8326V15.0128L2.34129 11.9686V6.83892L9.84647 4.14954V0H20.1529V4.14954L27.6587 6.83892V11.9686L30 15.0128V23.8326ZM15.0311 26.9808C19.1702 26.9808 22.5362 23.6242 22.5362 19.4953C22.5362 15.3671 19.1702 12.0099 15.0311 12.0099C10.8919 12.0099 7.52589 15.3464 7.52589 19.4953C7.52589 23.6449 10.8712 26.9808 15.0311 26.9808Z"
                    fill="#1B1A19"
                    fill-rule="evenodd"
                  />
                  <path
                    d="M57.3226 27.5991V5.89917H61.5079V27.5991H57.3226ZM44 27.5991V5.89917H48.1853V27.5991H44ZM46.7156 18.9637V14.9806H58.6964V18.9637H46.7156Z"
                    fill="#1B1A19"
                  />
                  <path
                    d="M72.3256 27.9178C70.7601 27.9178 69.4182 27.5673 68.2361 26.8981C67.086 26.2289 66.1594 25.3049 65.5205 24.094C64.8815 22.8831 64.562 21.4492 64.562 19.8241C64.562 18.199 64.8815 16.797 65.5205 15.5542C66.1594 14.3434 67.086 13.3874 68.2361 12.6864C69.4182 12.0173 70.7601 11.6667 72.3256 11.6667C73.891 11.6667 75.2648 12.0173 76.415 12.7183C77.5971 13.4193 78.4917 14.4071 79.1306 15.618C79.7696 16.8288 80.0891 18.2309 80.0891 19.7923C80.0891 20.0472 80.0891 20.2702 80.0571 20.4933C80.0571 20.7163 80.0252 20.8757 79.9932 21.035H67.9805V17.9122H76.8303L76.1913 19.4099C76.1913 18.1034 75.8719 17.02 75.2329 16.1915C74.5939 15.3631 73.6355 14.9488 72.3256 14.9488C71.1754 14.9488 70.2489 15.2993 69.546 16.0004C68.8431 16.7332 68.4917 17.6892 68.4917 18.8682V20.557C68.4917 21.7997 68.8431 22.7875 69.578 23.5204C70.2808 24.2215 71.2393 24.572 72.4853 24.572C73.5716 24.572 74.4342 24.3489 75.0412 23.9028C75.6482 23.4567 76.1913 22.915 76.6386 22.2459L79.546 23.9028C78.8431 25.2093 77.8846 26.1971 76.7025 26.8662C75.4885 27.5673 74.0188 27.9178 72.3256 27.9178Z"
                    fill="#1B1A19"
                  />
                  <path
                    d="M87.4456 28.0452C86.2954 28.0452 85.2731 27.8222 84.4105 27.3761C83.5479 26.9618 82.8769 26.3245 82.3977 25.5598C81.9504 24.7632 81.6948 23.8391 81.6948 22.7557C81.6948 21.7041 81.9504 20.7801 82.4616 19.9835C82.9408 19.1868 83.6437 18.5814 84.5063 18.1353C85.3689 17.7211 86.3593 17.498 87.4775 17.498C89.0111 17.498 90.2251 17.8166 91.1836 18.5177C92.1101 19.2187 92.7491 20.2065 93.1005 21.4811L91.1516 21.2262V17.4661C91.1516 16.8607 90.928 16.3509 90.5127 15.8729C90.0973 15.3949 89.3945 15.1719 88.436 15.1719C87.8609 15.1719 87.19 15.2356 86.4552 15.3949C85.7204 15.5224 84.9536 15.7773 84.1868 16.1597L82.9408 13.1007C83.8673 12.6546 84.8258 12.304 85.8801 12.0491C86.9025 11.7942 87.9248 11.6667 88.9791 11.6667C90.4168 11.6667 91.567 11.9217 92.5254 12.4315C93.452 12.9413 94.1868 13.6424 94.6341 14.5346C95.1133 15.4268 95.3369 16.4465 95.3369 17.5936V27.5991H92.0143L91.1197 24.6357L93.1005 24.0621C92.7171 25.3367 92.0782 26.2927 91.1197 26.9937C90.1293 27.6947 88.9152 28.0452 87.4456 28.0452ZM88.5958 24.8588C89.3625 24.8588 90.0015 24.6676 90.4488 24.2852C90.928 23.9028 91.1516 23.393 91.1516 22.7557C91.1516 22.1184 90.928 21.6086 90.4488 21.2262C90.0015 20.8438 89.3625 20.6526 88.5958 20.6526C87.829 20.6526 87.19 20.8438 86.7427 21.2262C86.2635 21.6086 86.0399 22.1184 86.0399 22.7557C86.0399 23.393 86.2635 23.9028 86.7427 24.2852C87.19 24.6676 87.829 24.8588 88.5958 24.8588Z"
                    fill="#1B1A19"
                  />
                  <path
                    d="M104.975 27.9815C103.57 27.9815 102.324 27.6309 101.237 26.9299C100.119 26.2289 99.2885 25.2729 98.6814 24.0302C98.0744 22.8194 97.7869 21.3854 97.7869 19.7922C97.7869 18.199 98.0744 16.7651 98.6814 15.5542C99.2885 14.3115 100.151 13.3555 101.237 12.6864C102.324 11.9853 103.57 11.6667 105.007 11.6667C106.381 11.6667 107.563 11.9853 108.586 12.6226C109.608 13.2599 110.407 14.184 110.982 15.3949C111.557 16.6057 111.844 18.0715 111.844 19.7922C111.844 21.4492 111.557 22.8831 110.982 24.1258C110.407 25.3367 109.608 26.2926 108.554 26.9618C107.531 27.6309 106.349 27.9815 104.975 27.9815ZM105.71 24.1258C106.796 24.1258 107.691 23.7116 108.362 22.915C109.065 22.1183 109.384 21.0987 109.384 19.8241C109.384 18.5176 109.065 17.4979 108.362 16.7013C107.691 15.9047 106.796 15.4905 105.71 15.4905C104.624 15.4905 103.761 15.9047 103.058 16.7013C102.387 17.4979 102.036 18.5176 102.036 19.7922C102.036 21.0668 102.387 22.1183 103.058 22.915C103.761 23.7116 104.624 24.1258 105.71 24.1258ZM110.247 27.5991L109.193 23.9028H109.48V16.0959H109.193V5.89917H113.378V27.5991H110.247Z"
                    fill="#1B1A19"
                  />
                  <path
                    d="M120.815 27.7584C120.112 27.7584 119.473 27.5991 118.866 27.3123C118.291 27.0255 117.812 26.5475 117.461 25.9421C117.109 25.3048 116.949 24.5082 116.949 23.4885V5.89917H121.103V22.0227C121.103 22.7556 121.231 23.2655 121.55 23.4885C121.838 23.7116 122.221 23.8072 122.636 23.8072V27.5353C122.413 27.5991 122.125 27.6628 121.806 27.6947C121.454 27.7265 121.135 27.7584 120.815 27.7584Z"
                    fill="#1B1A19"
                  />
                  <path
                    d="M129.815 28.0452C128.665 28.0452 127.643 27.8222 126.78 27.3761C125.917 26.9618 125.247 26.3245 124.767 25.5598C124.32 24.7632 124.064 23.8391 124.064 22.7557C124.064 21.7041 124.32 20.7801 124.831 19.9835C125.31 19.1868 126.013 18.5814 126.876 18.1353C127.739 17.7211 128.729 17.498 129.847 17.498C131.381 17.498 132.595 17.8166 133.553 18.5177C134.48 19.2187 135.119 20.2065 135.47 21.4811L133.521 21.2262V17.4661C133.521 16.8607 133.298 16.3509 132.882 15.8729C132.467 15.3949 131.764 15.1719 130.806 15.1719C130.231 15.1719 129.56 15.2356 128.825 15.3949C128.09 15.5224 127.323 15.7773 126.556 16.1597L125.31 13.1007C126.237 12.6546 127.195 12.304 128.25 12.0491C129.272 11.7942 130.294 11.6667 131.349 11.6667C132.786 11.6667 133.937 11.9217 134.895 12.4315C135.822 12.9413 136.556 13.6424 137.004 14.5346C137.483 15.4268 137.707 16.4465 137.707 17.5936V27.5991H134.384L133.489 24.6357L135.47 24.0621C135.087 25.3367 134.448 26.2927 133.489 26.9937C132.499 27.6947 131.285 28.0452 129.815 28.0452ZM130.965 24.8588C131.732 24.8588 132.371 24.6676 132.818 24.2852C133.298 23.9028 133.521 23.393 133.521 22.7557C133.521 22.1184 133.298 21.6086 132.818 21.2262C132.371 20.8438 131.732 20.6526 130.965 20.6526C130.199 20.6526 129.56 20.8438 129.112 21.2262C128.633 21.6086 128.409 22.1184 128.409 22.7557C128.409 23.393 128.633 23.9028 129.112 24.2852C129.56 24.6676 130.199 24.8588 130.965 24.8588Z"
                    fill="#1B1A19"
                  />
                  <path
                    d="M141.083 27.5991V12.0173H144.214L145.077 14.9807L143.735 14.9488C144.278 14.3115 144.885 13.738 145.556 13.26C146.227 12.7502 146.93 12.3678 147.728 12.081C148.495 11.7942 149.326 11.6667 150.156 11.6667C151.211 11.6667 152.073 11.8261 152.712 12.2084C153.351 12.559 153.831 13.0688 154.15 13.6742C154.438 14.2797 154.661 14.9807 154.789 15.7454C154.885 16.5421 154.949 17.3387 154.949 18.1672V27.5991H150.763V18.1353C150.763 17.1794 150.572 16.5421 150.188 16.2234C149.805 15.9048 149.358 15.7454 148.815 15.7454C148.048 15.7454 147.281 15.9685 146.578 16.4465C145.843 16.8926 145.236 17.4661 144.757 18.1672L144.214 16.0004H145.268V27.5991H141.083ZM160.412 27.5991V18.1353C160.412 17.1794 160.22 16.5421 159.869 16.2234C159.485 15.9048 159.038 15.7454 158.495 15.7454C157.728 15.7454 156.962 15.9685 156.259 16.4465C155.524 16.8926 154.917 17.4661 154.438 18.1672L153.415 14.9488C153.958 14.3115 154.565 13.738 155.236 13.26C155.907 12.7502 156.61 12.3678 157.409 12.081C158.176 11.7942 158.974 11.6667 159.837 11.6667C160.891 11.6667 161.722 11.8261 162.361 12.2084C163 12.559 163.479 13.0688 163.799 13.6742C164.118 14.2797 164.342 14.9807 164.438 15.7454C164.565 16.5421 164.629 17.3387 164.629 18.1672V27.5991H160.412Z"
                    fill="#1B1A19"
                  />
                  <path
                    d="M176.375 27.9815C175.001 27.9815 173.787 27.631 172.765 26.9937C171.742 26.3564 170.944 25.4323 170.401 24.2215C169.825 23.0106 169.538 21.5448 169.538 19.8241C169.538 18.1672 169.825 16.7014 170.401 15.4905C170.976 14.2797 171.774 13.3237 172.797 12.6546C173.819 11.9854 175.033 11.6667 176.407 11.6667C177.845 11.6667 179.091 12.0173 180.177 12.6864C181.231 13.3874 182.094 14.3434 182.701 15.5543C183.308 16.797 183.595 18.199 183.595 19.8241C183.595 21.4174 183.308 22.8513 182.669 24.0621C182.062 25.3049 181.231 26.2608 180.145 26.93C179.059 27.631 177.813 27.9815 176.375 27.9815ZM168.004 33.6534V12.0173H171.135L172.19 15.7136H171.87V23.5204H172.19V33.6534H168.004ZM175.672 24.1259C176.726 24.1259 177.621 23.7116 178.292 22.915C178.995 22.1184 179.346 21.0987 179.346 19.8241C179.346 18.5495 178.995 17.498 178.292 16.7014C177.621 15.9048 176.726 15.4905 175.672 15.4905C174.586 15.4905 173.723 15.9048 173.02 16.7014C172.317 17.498 171.966 18.5177 171.966 19.8241C171.966 21.0987 172.317 22.1184 173.02 22.915C173.723 23.7116 174.586 24.1259 175.672 24.1259Z"
                    fill="#1B1A19"
                  />
                </svg>
              </h1>
            </div>
            <div
              class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
            >
              <div
                class="MuiBox-root css-0"
              >
                <button
                  aria-label="Show build information"
                  class="MuiButtonBase-root MuiIconButton-root MuiIconButton-sizeSmall css-hvz71z-MuiButtonBase-root-MuiIconButton-root"
                  tabindex="0"
                  type="button"
                >
                  <span
                    class="MuiTouchRipple-root css-8je8zh-MuiTouchRipple-root"
                  />
                </button>
              </div>
            </div>
          </div>
        </h2>
        <div
          class="MuiDialogContent-root MuiDialogContent-dividers css-gl9hfx-MuiDialogContent-root"
        >
          <h2
            class="MuiTypography-root MuiTypography-h6 MuiDialogTitle-root css-8yphvn-MuiTypography-root-MuiDialogTitle-root"
            id="chooser-dialog-title"
            style="display: flex;"
          >
            <div
              class="MuiGrid-root MuiGrid-container css-9cyib4-MuiGrid-root"
            >
              <div
                class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
              >
                <h1
                  class="MuiTypography-root MuiTypography-h1 css-1kazmbo-MuiTypography-root"
                  style="font-size: 1.25rem; font-weight: 500; line-height: 1.6;"
                  tabindex="-1"
                >
                  Choose a cluster
                </h1>
              </div>
            </div>
          </h2>
          <div
            class="MuiContainer-root MuiContainer-maxWidthLg css-cnms5y-MuiContainer-root"
            style="max-width: 500px; padding-bottom: 16px;"
          >
            <div
              class="MuiGrid-root MuiGrid-container MuiGrid-spacing-xs-4 MuiGrid-direction-xs-column css-1w41v25-MuiGrid-root"
            >
              <div
                aria-labelledby="#recent-clusters-label"
                class="MuiGrid-root MuiGrid-container MuiGrid-item MuiGrid-spacing-xs-2 css-1mqp8pp-MuiGrid-root"
              >
                <div
                  class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                >
                  <button
                    class="MuiButtonBase-root css-10d1a0h-MuiButtonBase-root"
                    tabindex="0"
                    type="button"
                  >
                    <div
                      class="MuiPaper-root MuiPaper-outlined MuiPaper-rounded MuiCard-root css-jxx1xv-MuiPaper-root-MuiCard-root"
                    >
                      <div
                        class="MuiCardContent-root css-d64700-MuiCardContent-root"
                      >
                        <p
                          class="MuiTypography-root MuiTypography-body1 MuiTypography-gutterBottom css-18vfeyy-MuiTypography-root"
                          title="only-cluster"
                        >
                          only-cluster
                        </p>
                      </div>
                    </div>
                    <span
                      class="MuiTouchRipple-root css-8je8zh-MuiTouchRipple-root"
                    />
                  </button>
                </div>
                <div
                  class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                >
                  <button
                    class="MuiButtonBase-root css-10d1a0h-MuiButtonBase-root"
                    tabindex="0"
                    type="button"
                  >
                    <div
                      class="MuiPaper-root MuiPaper-outlined MuiPaper-rounded MuiCard-root css-jxx1xv-MuiPaper-root-MuiCard-root"
                    >
                      <div
                        class="MuiCardContent-root css-d64700-MuiCardContent-root"
                      >
                        <p
                          class="MuiTypography-root MuiTypography-body1 MuiTypography-gutterBottom css-18vfeyy-MuiTypography-root"
                          title="only-cluster"
                        >
                          only-cluster
                        </p>
                      </div>
                    </div>
                    <span
                      class="MuiTouchRipple-root css-8je8zh-MuiTouchRipple-root"
                    />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      data-testid="sentinelEnd"
      tabindex="0"
    />
  </div>
</body>