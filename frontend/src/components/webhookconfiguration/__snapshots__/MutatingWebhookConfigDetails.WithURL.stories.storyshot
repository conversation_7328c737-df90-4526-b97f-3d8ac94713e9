<body>
  <div>
    <div
      class="MuiGrid-root MuiGrid-container MuiGrid-spacing-xs-1 css-bsyb48-MuiGrid-root"
    >
      <div
        class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 css-49904w-MuiGrid-root"
      >
        <div
          class="MuiBox-root css-p0cik4"
        >
          <button
            class="MuiButtonBase-root MuiButton-root MuiButton-text Mu<PERSON><PERSON><PERSON>on-textPrimary MuiButton-sizeSmall MuiButton-textSizeSmall MuiButton-colorPrimary MuiButton-disableElevation MuiButton-root MuiButton-text MuiButton-textPrimary MuiButton-sizeSmall MuiButton-textSizeSmall MuiButton-colorPrimary MuiButton-disableElevation css-1j11y9k-MuiButtonBase-root-MuiButton-root"
            tabindex="0"
            type="button"
          >
            <span
              class="MuiButton-icon MuiButton-startIcon MuiButton-iconSizeSmall css-y6rp3m-MuiButton-startIcon"
            />
            <p
              class="MuiTypography-root MuiTypography-body1 css-1ezega9-MuiTypography-root"
              style="padding-top: 3px;"
            >
              Back
            </p>
            <span
              class="MuiTouchRipple-root css-8je8zh-MuiTouchRipple-root"
            />
          </button>
        </div>
      </div>
      <div
        class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 css-49904w-MuiGrid-root"
      >
        <div
          class="MuiBox-root css-p0cik4"
        >
          <div
            class="MuiGrid-root MuiGrid-container MuiGrid-spacing-xs-2 css-1ts0dnm-MuiGrid-root"
          >
            <div
              class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
            >
              <div
                class="MuiBox-root css-70qvj9"
              >
                <h1
                  class="MuiTypography-root MuiTypography-h1 MuiTypography-noWrap css-yeaech-MuiTypography-root"
                >
                  MutatingWebhookConfiguration: webhook-admission-controller
                </h1>
                <div
                  class="MuiBox-root css-ldp2l3"
                />
              </div>
            </div>
            <div
              class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
            >
              <div
                class="MuiGrid-root MuiGrid-container MuiGrid-item css-ztq4zc-MuiGrid-root"
              >
                <div
                  class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                />
                <div
                  class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                />
                <div
                  class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                >
                  <button
                    aria-label="Edit"
                    class="MuiButtonBase-root MuiIconButton-root MuiIconButton-sizeMedium css-whz9ym-MuiButtonBase-root-MuiIconButton-root"
                    data-mui-internal-clone-element="true"
                    tabindex="0"
                    type="button"
                  >
                    <span
                      class="MuiTouchRipple-root css-8je8zh-MuiTouchRipple-root"
                    />
                  </button>
                </div>
                <div
                  class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                >
                  <button
                    aria-label="Delete"
                    class="MuiButtonBase-root MuiIconButton-root MuiIconButton-sizeMedium css-whz9ym-MuiButtonBase-root-MuiIconButton-root"
                    data-mui-internal-clone-element="true"
                    tabindex="0"
                    type="button"
                  >
                    <span
                      class="MuiTouchRipple-root css-8je8zh-MuiTouchRipple-root"
                    />
                  </button>
                  <div />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 css-49904w-MuiGrid-root"
      >
        <div
          class="MuiBox-root css-p0cik4"
        >
          <div
            class="MuiBox-root css-j1fy4m"
          >
            <div
              aria-busy="false"
              aria-live="polite"
              class="MuiBox-root css-1txv3mw"
            >
              <div
                class="MuiBox-root css-0"
              >
                <dl
                  class="MuiGrid-root MuiGrid-container css-kxuems-MuiGrid-root"
                >
                  <dt
                    class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-4 css-1iczkge-MuiGrid-root"
                  >
                    Name
                  </dt>
                  <dd
                    class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-8 css-deb4a-MuiGrid-root"
                  >
                    <span
                      class="MuiTypography-root MuiTypography-body1 css-e06lsu-MuiTypography-root"
                    >
                      webhook-admission-controller
                    </span>
                  </dd>
                  <dt
                    class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-4 css-1iczkge-MuiGrid-root"
                  >
                    Creation
                  </dt>
                  <dd
                    class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-8 css-deb4a-MuiGrid-root"
                  >
                    <span
                      class="MuiTypography-root MuiTypography-body1 css-e06lsu-MuiTypography-root"
                    >
                      2022-10-14T11:25:22.000Z
                    </span>
                  </dd>
                  <dt
                    class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-4 css-1iczkge-MuiGrid-root"
                  >
                    Labels
                  </dt>
                  <dd
                    class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-8 css-deb4a-MuiGrid-root"
                  >
                    <div
                      class="MuiBox-root css-yi3mkw"
                    >
                      <p
                        class="MuiTypography-root MuiTypography-body1 css-1on669h-MuiTypography-root"
                      >
                        admissions.enforcer/disabled: true
                      </p>
                    </div>
                  </dd>
                  <dt
                    class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-4 css-1iczkge-MuiGrid-root"
                  >
                    API Version
                  </dt>
                  <dd
                    class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-8 css-deb4a-MuiGrid-root"
                  />
                  <dt
                    class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-4 css-iqixpy-MuiGrid-root"
                  >
                    Webhooks
                  </dt>
                  <dd
                    class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-8 css-1xrovmc-MuiGrid-root"
                  >
                    1
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 css-49904w-MuiGrid-root"
      >
        <div
          class="MuiBox-root css-p0cik4"
        >
          <div
            class="MuiBox-root css-j1fy4m"
          >
            <div
              class="MuiGrid-root MuiGrid-container MuiGrid-spacing-xs-2 css-1ts0dnm-MuiGrid-root"
            >
              <div
                class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
              >
                <div
                  class="MuiBox-root css-70qvj9"
                >
                  <h2
                    class="MuiTypography-root MuiTypography-h2 MuiTypography-noWrap css-m5vcfd-MuiTypography-root"
                  >
                    Webhooks
                  </h2>
                  <div
                    class="MuiBox-root css-ldp2l3"
                  />
                </div>
              </div>
            </div>
            <div
              class="MuiBox-root css-1txv3mw"
            >
              <dl
                class="MuiGrid-root MuiGrid-container css-kxuems-MuiGrid-root"
              >
                <dt
                  class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-4 css-1iczkge-MuiGrid-root"
                >
                  Name
                </dt>
                <dd
                  class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-8 css-deb4a-MuiGrid-root"
                >
                  <span
                    class="MuiTypography-root MuiTypography-body1 css-e06lsu-MuiTypography-root"
                  >
                    webhook-admission-controller.example.com
                  </span>
                </dd>
                <dt
                  class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-4 css-1iczkge-MuiGrid-root"
                >
                  Admission Review Versions
                </dt>
                <dd
                  class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-8 css-deb4a-MuiGrid-root"
                >
                  <span
                    class="MuiTypography-root MuiTypography-body1 css-e06lsu-MuiTypography-root"
                  >
                    v1beta1
                  </span>
                </dd>
                <dt
                  class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-4 css-1iczkge-MuiGrid-root"
                >
                  Client Config: URL
                </dt>
                <dd
                  class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-8 css-deb4a-MuiGrid-root"
                >
                  <span
                    class="MuiTypography-root MuiTypography-body1 css-e06lsu-MuiTypography-root"
                  >
                    https://localhost:8443/mutate-nodes
                  </span>
                </dd>
                <dt
                  class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-4 css-1iczkge-MuiGrid-root"
                >
                  Client Config: Ca Bundle
                </dt>
                <dd
                  class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-8 css-deb4a-MuiGrid-root"
                >
                  <div
                    class="MuiGrid-root MuiGrid-container MuiGrid-spacing-xs-2 css-1fwwhll-MuiGrid-root"
                  >
                    <div
                      class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                    >
                      <button
                        aria-label="toggle field visibility"
                        class="MuiButtonBase-root MuiIconButton-root MuiIconButton-edgeEnd MuiIconButton-sizeMedium css-1d2b2o6-MuiButtonBase-root-MuiIconButton-root"
                        tabindex="0"
                        type="button"
                      >
                        <span
                          class="MuiTouchRipple-root css-8je8zh-MuiTouchRipple-root"
                        />
                      </button>
                    </div>
                    <div
                      class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-true css-1z0dvx8-MuiGrid-root"
                    >
                      <div
                        class="MuiInputBase-root MuiInput-root MuiInput-underline MuiInputBase-colorPrimary MuiInputBase-fullWidth Mui-readOnly MuiInputBase-readOnly css-zam05p-MuiInputBase-root-MuiInput-root"
                      >
                        <input
                          class="MuiInputBase-input MuiInput-input Mui-readOnly MuiInputBase-readOnly css-1x51dt5-MuiInputBase-input-MuiInput-input"
                          readonly=""
                          type="password"
                          value="******"
                        />
                      </div>
                    </div>
                  </div>
                </dd>
                <dt
                  class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-4 css-1iczkge-MuiGrid-root"
                >
                  Failure Policy
                </dt>
                <dd
                  class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-8 css-deb4a-MuiGrid-root"
                >
                  <span
                    class="MuiTypography-root MuiTypography-body1 css-e06lsu-MuiTypography-root"
                  >
                    Fail
                  </span>
                </dd>
                <dt
                  class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-4 css-1iczkge-MuiGrid-root"
                >
                  Match Policy
                </dt>
                <dd
                  class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-8 css-deb4a-MuiGrid-root"
                >
                  <span
                    class="MuiTypography-root MuiTypography-body1 css-e06lsu-MuiTypography-root"
                  >
                    Exact
                  </span>
                </dd>
                <dt
                  class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-4 css-1iczkge-MuiGrid-root"
                >
                  Side Effects
                </dt>
                <dd
                  class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-8 css-deb4a-MuiGrid-root"
                >
                  <span
                    class="MuiTypography-root MuiTypography-body1 css-e06lsu-MuiTypography-root"
                  >
                    NoneOnDryRun
                  </span>
                </dd>
                <dt
                  class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-4 css-1iczkge-MuiGrid-root"
                >
                  Timeout Seconds
                </dt>
                <dd
                  class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-8 css-deb4a-MuiGrid-root"
                >
                  <span
                    class="MuiTypography-root MuiTypography-body1 css-e06lsu-MuiTypography-root"
                  >
                    10
                  </span>
                </dd>
                <dt
                  class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-4 css-1iczkge-MuiGrid-root"
                >
                  Namespace Selector
                </dt>
                <dd
                  class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-8 css-deb4a-MuiGrid-root"
                >
                  <p
                    class="MuiTypography-root MuiTypography-body1 css-cwic1g-MuiTypography-root"
                  >
                    validating-webhook: true
                  </p>
                  <p
                    class="MuiTypography-root MuiTypography-body1 css-cwic1g-MuiTypography-root"
                  >
                    validating-webhook in (true)
                  </p>
                </dd>
                <dt
                  class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-4 css-1iczkge-MuiGrid-root"
                >
                  Object Selector
                </dt>
                <dd
                  class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-8 css-deb4a-MuiGrid-root"
                >
                  <p
                    class="MuiTypography-root MuiTypography-body1 css-cwic1g-MuiTypography-root"
                  >
                    validating-webhook: true
                  </p>
                  <p
                    class="MuiTypography-root MuiTypography-body1 css-cwic1g-MuiTypography-root"
                  >
                    validating-webhook in (true)
                  </p>
                </dd>
                <dt
                  class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-4 css-1iczkge-MuiGrid-root"
                >
                  Reinvocation Policy
                </dt>
                <dd
                  class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-8 css-deb4a-MuiGrid-root"
                >
                  <span
                    class="MuiTypography-root MuiTypography-body1 css-e06lsu-MuiTypography-root"
                  >
                    Never
                  </span>
                </dd>
                <dt
                  class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-4 css-iqixpy-MuiGrid-root"
                >
                  Rules
                </dt>
                <dd
                  class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-8 css-1xrovmc-MuiGrid-root"
                >
                  <div
                    class="MuiPaper-root MuiPaper-outlined MuiPaper-rounded MuiTableContainer-root css-onzayo-MuiPaper-root-MuiTableContainer-root"
                  >
                    <table
                      class="MuiTable-root css-r7i92b-MuiTable-root"
                    >
                      <thead
                        class="MuiTableHead-root css-15wwp11-MuiTableHead-root"
                      >
                        <tr
                          class="MuiTableRow-root MuiTableRow-head css-13jktim-MuiTableRow-root"
                        >
                          <th
                            class="MuiTableCell-root MuiTableCell-head MuiTableCell-sizeSmall css-l4jzay-MuiTableCell-root"
                            scope="col"
                          >
                            API Groups
                          </th>
                          <th
                            class="MuiTableCell-root MuiTableCell-head MuiTableCell-sizeSmall css-l4jzay-MuiTableCell-root"
                            scope="col"
                          >
                            API Versions
                          </th>
                          <th
                            class="MuiTableCell-root MuiTableCell-head MuiTableCell-sizeSmall css-l4jzay-MuiTableCell-root"
                            scope="col"
                          >
                            Operations
                          </th>
                          <th
                            class="MuiTableCell-root MuiTableCell-head MuiTableCell-sizeSmall css-l4jzay-MuiTableCell-root"
                            scope="col"
                          >
                            Resources
                          </th>
                          <th
                            class="MuiTableCell-root MuiTableCell-head MuiTableCell-sizeSmall css-l4jzay-MuiTableCell-root"
                            scope="col"
                          >
                            Scope
                          </th>
                        </tr>
                      </thead>
                      <tbody
                        class="MuiTableBody-root css-apqrd9-MuiTableBody-root"
                      >
                        <tr
                          class="MuiTableRow-root css-13jktim-MuiTableRow-root"
                        >
                          <td
                            class="MuiTableCell-root MuiTableCell-body MuiTableCell-sizeSmall css-oh2q4q-MuiTableCell-root"
                          />
                          <td
                            class="MuiTableCell-root MuiTableCell-body MuiTableCell-sizeSmall css-oh2q4q-MuiTableCell-root"
                          >
                            v1
                          </td>
                          <td
                            class="MuiTableCell-root MuiTableCell-body MuiTableCell-sizeSmall css-oh2q4q-MuiTableCell-root"
                          >
                            CREATE
                          </td>
                          <td
                            class="MuiTableCell-root MuiTableCell-body MuiTableCell-sizeSmall css-oh2q4q-MuiTableCell-root"
                          >
                            pods
                          </td>
                          <td
                            class="MuiTableCell-root MuiTableCell-body MuiTableCell-sizeSmall css-oh2q4q-MuiTableCell-root"
                          >
                            *
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </dd>
              </dl>
            </div>
          </div>
        </div>
      </div>
      <div
        class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 css-49904w-MuiGrid-root"
      >
        <div
          class="MuiBox-root css-p0cik4"
        >
          <div
            class="MuiBox-root css-j1fy4m"
          >
            <div
              class="MuiGrid-root MuiGrid-container MuiGrid-spacing-xs-2 css-1ts0dnm-MuiGrid-root"
            >
              <div
                class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
              >
                <div
                  class="MuiBox-root css-70qvj9"
                >
                  <h2
                    class="MuiTypography-root MuiTypography-h2 MuiTypography-noWrap css-m5vcfd-MuiTypography-root"
                  >
                    Events
                  </h2>
                  <div
                    class="MuiBox-root css-ldp2l3"
                  />
                </div>
              </div>
            </div>
            <div
              class="MuiBox-root css-1txv3mw"
            >
              <div
                class="MuiPaper-root MuiPaper-outlined MuiPaper-rounded css-1guobrs-MuiPaper-root"
              >
                <div
                  class="MuiBox-root css-19midj6"
                >
                  <p
                    class="MuiTypography-root MuiTypography-body1 MuiTypography-alignCenter css-18lkse1-MuiTypography-root"
                  >
                    No data to be shown.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    ;
  </div>
</body>