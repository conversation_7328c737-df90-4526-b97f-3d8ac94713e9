<body>
  <div>
    <div
      class="MuiBox-root css-1yvazsw"
    >
      <div
        class="MuiBox-root css-2gjmrx"
      >
        <div
          class="MuiBox-root css-pb8oes"
        >
          <div
            class="MuiAutocomplete-root MuiAutocomplete-hasPopupIcon css-1x6bjyf-MuiAutocomplete-root"
          >
            <div
              class="MuiBox-root css-1dipl1t"
            >
              <div
                class="MuiFormControl-root MuiFormControl-fullWidth MuiTextField-root css-wb57ya-MuiFormControl-root-MuiTextField-root"
                style="margin-top: 0px;"
              >
                <label
                  class="MuiFormLabel-root MuiInputLabel-root MuiInputLabel-formControl MuiInputLabel-animated MuiInputLabel-shrink MuiInputLabel-sizeSmall MuiInputLabel-outlined MuiFormLabel-colorPrimary MuiInputLabel-root MuiInputLabel-formControl MuiInputLabel-animated MuiInputLabel-shrink MuiInputLabel-sizeSmall MuiInputLabel-outlined css-81cxhg-MuiFormLabel-root-MuiInputLabel-root"
                  data-shrink="true"
                  for="namespaces-filter"
                  id="namespaces-filter-label"
                >
                  Namespaces
                </label>
                <div
                  class="MuiInputBase-root MuiOutlinedInput-root MuiInputBase-colorPrimary MuiInputBase-fullWidth MuiInputBase-formControl MuiInputBase-sizeSmall MuiInputBase-adornedEnd MuiAutocomplete-inputRoot css-b1xigt-MuiInputBase-root-MuiOutlinedInput-root"
                >
                  <input
                    aria-autocomplete="both"
                    aria-expanded="false"
                    aria-invalid="false"
                    autocapitalize="none"
                    autocomplete="off"
                    class="MuiInputBase-input MuiOutlinedInput-input MuiInputBase-inputSizeSmall MuiInputBase-inputAdornedEnd MuiAutocomplete-input MuiAutocomplete-inputFocused css-19qh8xo-MuiInputBase-input-MuiOutlinedInput-input"
                    id="namespaces-filter"
                    placeholder="Filter"
                    role="combobox"
                    spellcheck="false"
                    type="text"
                    value=""
                  />
                  <div
                    class="MuiAutocomplete-endAdornment css-p1olib-MuiAutocomplete-endAdornment"
                  >
                    <button
                      aria-label="Open"
                      class="MuiButtonBase-root MuiIconButton-root MuiIconButton-sizeMedium MuiAutocomplete-popupIndicator css-qzbt6i-MuiButtonBase-root-MuiIconButton-root-MuiAutocomplete-popupIndicator"
                      tabindex="-1"
                      title="Open"
                      type="button"
                    >
                      <svg
                        aria-hidden="true"
                        class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-i4bv87-MuiSvgIcon-root"
                        data-testid="ArrowDropDownIcon"
                        focusable="false"
                        viewBox="0 0 24 24"
                      >
                        <path
                          d="M7 10l5 5 5-5z"
                        />
                      </svg>
                      <span
                        class="MuiTouchRipple-root css-8je8zh-MuiTouchRipple-root"
                      />
                    </button>
                  </div>
                  <fieldset
                    aria-hidden="true"
                    class="MuiOutlinedInput-notchedOutline css-1d3z3hw-MuiOutlinedInput-notchedOutline"
                  >
                    <legend
                      class="css-14lo706"
                    >
                      <span>
                        Namespaces
                      </span>
                    </legend>
                  </fieldset>
                </div>
              </div>
            </div>
          </div>
          <div
            class="MuiButtonBase-root MuiChip-root MuiChip-filled MuiChip-sizeMedium MuiChip-colorPrimary MuiChip-clickable MuiChip-clickableColorPrimary MuiChip-filledPrimary css-qsy4j-MuiButtonBase-root-MuiChip-root"
            role="button"
            tabindex="0"
          >
            <span
              class="MuiChip-label MuiChip-labelMedium css-6od3lo-MuiChip-label"
            >
              <div
                class="MuiStack-root css-1qxw92a-MuiStack-root"
              >
                 
                Pods
                 
              </div>
            </span>
            <span
              class="MuiTouchRipple-root css-8je8zh-MuiTouchRipple-root"
            />
          </div>
          <div
            class="MuiBox-root css-19ideir"
          >
            Group By
          </div>
          <div
            class="MuiBox-root css-esugir"
          >
            <div
              class="MuiButtonBase-root MuiChip-root MuiChip-filled MuiChip-sizeMedium MuiChip-colorPrimary MuiChip-clickable MuiChip-clickableColorPrimary MuiChip-filledPrimary css-qsy4j-MuiButtonBase-root-MuiChip-root"
              role="button"
              tabindex="0"
            >
              <span
                class="MuiChip-label MuiChip-labelMedium css-6od3lo-MuiChip-label"
              >
                Namespace
              </span>
              <span
                class="MuiTouchRipple-root css-8je8zh-MuiTouchRipple-root"
              />
            </div>
            <div
              class="MuiButtonBase-root MuiChip-root MuiChip-outlined MuiChip-sizeMedium MuiChip-colorDefault MuiChip-clickable MuiChip-clickableColorDefault MuiChip-outlinedDefault css-o4yg6p-MuiButtonBase-root-MuiChip-root"
              role="button"
              tabindex="0"
            >
              <span
                class="MuiChip-label MuiChip-labelMedium css-1jzq0dw-MuiChip-label"
              >
                Instance
              </span>
              <span
                class="MuiTouchRipple-root css-8je8zh-MuiTouchRipple-root"
              />
            </div>
            <div
              class="MuiButtonBase-root MuiChip-root MuiChip-outlined MuiChip-sizeMedium MuiChip-colorDefault MuiChip-clickable MuiChip-clickableColorDefault MuiChip-outlinedDefault css-o4yg6p-MuiButtonBase-root-MuiChip-root"
              role="button"
              tabindex="0"
            >
              <span
                class="MuiChip-label MuiChip-labelMedium css-1jzq0dw-MuiChip-label"
              >
                Node
              </span>
              <span
                class="MuiTouchRipple-root css-8je8zh-MuiTouchRipple-root"
              />
            </div>
          </div>
          <div
            class="MuiButtonBase-root MuiChip-root MuiChip-outlined MuiChip-sizeMedium MuiChip-colorDefault MuiChip-clickable MuiChip-clickableColorDefault MuiChip-outlinedDefault css-o4yg6p-MuiButtonBase-root-MuiChip-root"
            role="button"
            tabindex="0"
          >
            <span
              class="MuiChip-label MuiChip-labelMedium css-1jzq0dw-MuiChip-label"
            >
              Status: Error or Warning
            </span>
            <span
              class="MuiTouchRipple-root css-8je8zh-MuiTouchRipple-root"
            />
          </div>
          <div
            class="MuiButtonBase-root MuiChip-root MuiChip-outlined MuiChip-sizeMedium MuiChip-colorDefault MuiChip-clickable MuiChip-clickableColorDefault MuiChip-outlinedDefault css-o4yg6p-MuiButtonBase-root-MuiChip-root"
            role="button"
            tabindex="0"
          >
            <span
              class="MuiChip-label MuiChip-labelMedium css-1jzq0dw-MuiChip-label"
            >
              Expand All
            </span>
            <span
              class="MuiTouchRipple-root css-8je8zh-MuiTouchRipple-root"
            />
          </div>
        </div>
        <div
          style="flex-grow: 1;"
        >
          <div
            class="react-flow light"
            data-testid="rf__wrapper"
            style="width: 100%; height: 100%; overflow: hidden; position: relative; z-index: 0;"
          >
            <div
              class="react-flow__renderer"
              style="position: absolute; width: 100%; height: 100%; top: 0px; left: 0px;"
            >
              <div
                class="react-flow__pane draggable"
                style="position: absolute; width: 100%; height: 100%; top: 0px; left: 0px;"
              >
                <div
                  class="react-flow__viewport xyflow__viewport react-flow__container"
                  style="transform: translate(50px,56px) scale(1);"
                >
                  <div
                    class="react-flow__edges"
                  />
                  <div
                    class="react-flow__edgelabel-renderer"
                  />
                  <div
                    class="react-flow__nodes"
                    style="position: absolute; width: 100%; height: 100%; top: 0px; left: 0px;"
                  >
                    <div
                      aria-describedby="react-flow__node-desc-1"
                      class="react-flow__node react-flow__node-object selectable"
                      data-id="custon-node-2"
                      data-testid="rf__node-custon-node-2"
                      style="z-index: 0; transform: translate(24px,24px); pointer-events: all; visibility: visible; width: 220px; height: 70px;"
                    >
                      <div
                        class="css-1dpk5ph"
                        role="button"
                        tabindex="0"
                      >
                        <div
                          class="react-flow__handle react-flow__handle-top nodrag nopan target connectable connectablestart connectableend connectionindicator"
                          data-handlepos="top"
                          data-id="1-custon-node-2-null-target"
                          data-nodeid="custon-node-2"
                          style="opacity: 0;"
                        />
                        <div
                          class="react-flow__handle react-flow__handle-bottom nodrag nopan source connectable connectablestart connectableend connectionindicator"
                          data-handlepos="bottom"
                          data-id="1-custon-node-2-null-source"
                          data-nodeid="custon-node-2"
                          style="opacity: 0;"
                        />
                        <div
                          class="css-1svjhx1"
                        >
                          2
                        </div>
                        <div
                          class="css-1irety8"
                        >
                          <div
                            class="css-1o5h0m"
                          >
                            <div
                              class="css-x2hlid"
                            />
                            <div
                              class="css-estfx9"
                            >
                              Node with children
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div
                      aria-describedby="react-flow__node-desc-1"
                      class="react-flow__node react-flow__node-object selectable parent"
                      data-id="123456"
                      data-testid="rf__node-123456"
                      style="z-index: 0; transform: translate(24px,114px); pointer-events: all; visibility: visible; width: 268px; height: 118px;"
                    >
                      <div
                        class="css-1ac5zp6"
                        role="button"
                        tabindex="0"
                      >
                        <div
                          class="css-1g5rcnn"
                          title="default"
                        >
                          <div
                            class="MuiBox-root css-wjpmq5"
                          >
                            <svg
                              height="17.500378mm"
                              id="svg13826"
                              inkscape:version="0.91 r13725"
                              sodipodi:docname="pod.svg"
                              style="scale: 1.1; width: 100%; height: 100%;"
                              viewBox="0 0 18.035334 17.500378"
                              width="18.035334mm"
                              xmlns="http://www.w3.org/2000/svg"
                              xmlns:cc="http://creativecommons.org/ns#"
                              xmlns:dc="http://purl.org/dc/elements/1.1/"
                              xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
                              xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
                              xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
                              xmlns:svg="http://www.w3.org/2000/svg"
                            >
                              <defs
                                id="defs13820"
                              />
                              <sodipodi:namedview
                                bordercolor="#666666"
                                borderopacity="1"
                                fit-margin-bottom="0"
                                fit-margin-left="0"
                                fit-margin-right="0"
                                fit-margin-top="0"
                                id="base"
                                inkscape:current-layer="layer1"
                                inkscape:cx="-2.090004"
                                inkscape:cy="33.752239"
                                inkscape:document-units="mm"
                                inkscape:pageopacity="0"
                                inkscape:pageshadow="2"
                                inkscape:window-height="775"
                                inkscape:window-maximized="1"
                                inkscape:window-width="1440"
                                inkscape:window-x="0"
                                inkscape:window-y="1"
                                inkscape:zoom="8"
                                pagecolor="currentcolor"
                                showgrid="false"
                              />
                              <metadata
                                id="metadata13823"
                              >
                                <rdf:rdf>
                                  <cc:work
                                    rdf:about=""
                                  >
                                    <dc:format>
                                      image/svg+xml
                                    </dc:format>
                                    <dc:type
                                      rdf:resource="http://purl.org/dc/dcmitype/StillImage"
                                    />
                                    <dc:title />
                                  </cc:work>
                                </rdf:rdf>
                              </metadata>
                              <g
                                id="layer1"
                                inkscape:groupmode="layer"
                                inkscape:label="Calque 1"
                                transform="translate(-0.99262638,-1.174181)"
                              >
                                <g
                                  id="g70"
                                  transform="matrix(1.0148887,0,0,1.0148887,16.902146,-2.698726)"
                                >
                                  <path
                                    d="m -6.8492015,4.2724668 a 1.1191255,1.1099671 0 0 0 -0.4288818,0.1085303 l -5.8524037,2.7963394 a 1.1191255,1.1099671 0 0 0 -0.605524,0.7529759 l -1.443828,6.2812846 a 1.1191255,1.1099671 0 0 0 0.151943,0.851028 1.1191255,1.1099671 0 0 0 0.06362,0.08832 l 4.0508,5.036555 a 1.1191255,1.1099671 0 0 0 0.874979,0.417654 l 6.4961011,-0.0015 a 1.1191255,1.1099671 0 0 0 0.8749788,-0.416906 L 1.3818872,15.149453 A 1.1191255,1.1099671 0 0 0 1.5981986,14.210104 L 0.15212657,7.9288154 A 1.1191255,1.1099671 0 0 0 -0.45339794,7.1758396 L -6.3065496,4.3809971 A 1.1191255,1.1099671 0 0 0 -6.8492015,4.2724668 Z"
                                    id="path3055"
                                    inkscape:connector-curvature="0"
                                    inkscape:export-filename="new.png"
                                    inkscape:export-xdpi="250.55"
                                    inkscape:export-ydpi="250.55"
                                    style="fill: transparent; fill-opacity: 1; stroke: none; stroke-width: 0; stroke-miterlimit: 4; stroke-dasharray: none; stroke-opacity: 1;"
                                  />
                                  <path
                                    d="M -6.8523435,3.8176372 A 1.1814304,1.171762 0 0 0 -7.3044284,3.932904 l -6.1787426,2.9512758 a 1.1814304,1.171762 0 0 0 -0.639206,0.794891 l -1.523915,6.6308282 a 1.1814304,1.171762 0 0 0 0.160175,0.89893 1.1814304,1.171762 0 0 0 0.06736,0.09281 l 4.276094,5.317236 a 1.1814304,1.171762 0 0 0 0.92363,0.440858 l 6.8576188,-0.0015 a 1.1814304,1.171762 0 0 0 0.9236308,-0.44011 l 4.2745966,-5.317985 a 1.1814304,1.171762 0 0 0 0.228288,-0.990993 L 0.53894439,7.6775738 A 1.1814304,1.171762 0 0 0 -0.10026101,6.8834313 L -6.2790037,3.9321555 A 1.1814304,1.171762 0 0 0 -6.8523435,3.8176372 Z m 0.00299,0.4550789 a 1.1191255,1.1099671 0 0 1 0.5426517,0.1085303 l 5.85315169,2.7948425 A 1.1191255,1.1099671 0 0 1 0.15197811,7.9290648 L 1.598051,14.21035 a 1.1191255,1.1099671 0 0 1 -0.2163123,0.939348 l -4.0493032,5.037304 a 1.1191255,1.1099671 0 0 1 -0.8749789,0.416906 l -6.4961006,0.0015 a 1.1191255,1.1099671 0 0 1 -0.874979,-0.417652 l -4.0508,-5.036554 a 1.1191255,1.1099671 0 0 1 -0.06362,-0.08832 1.1191255,1.1099671 0 0 1 -0.151942,-0.851028 l 1.443827,-6.2812853 a 1.1191255,1.1099671 0 0 1 0.605524,-0.7529758 l 5.8524036,-2.7963395 a 1.1191255,1.1099671 0 0 1 0.4288819,-0.1085303 z"
                                    id="path3054-2-9"
                                    inkscape:connector-curvature="0"
                                    style="color: rgb(0, 0, 0); font-style: normal; font-variant: normal; font-weight: normal; font-stretch: normal; font-size: medium; line-height: normal; font-family: Sans; text-indent: 0; text-align: start; text-decoration: none; text-decoration-line: none; letter-spacing: normal; word-spacing: normal; text-transform: none; writing-mode: lr-tb; direction: ltr; baseline-shift: baseline; text-anchor: start; display: inline; overflow: visible; visibility: visible; fill: currentcolor; fill-opacity: 1; fill-rule: nonzero; stroke: none; stroke-width: 0; stroke-miterlimit: 4; stroke-dasharray: none; marker: none; enable-background: accumulate;"
                                  />
                                </g>
                                <g
                                  id="g3341"
                                  transform="translate(0.12766661,0.35147801)"
                                >
                                  <path
                                    d="M 6.2617914,7.036086 9.8826317,5.986087 13.503462,7.036086 9.8826317,8.086087 Z"
                                    id="path910"
                                    inkscape:connector-curvature="0"
                                    inkscape:export-xdpi="376.57999"
                                    inkscape:export-ydpi="376.57999"
                                    style="fill: currentcolor; fill-rule: evenodd; stroke: none; stroke-width: 0.26458332; stroke-linecap: square; stroke-miterlimit: 10;"
                                  />
                                  <path
                                    d="m 6.2617914,7.43817 0,3.852778 3.3736103,1.868749 0.0167,-4.713193 z"
                                    id="path912"
                                    inkscape:connector-curvature="0"
                                    inkscape:export-xdpi="376.57999"
                                    inkscape:export-ydpi="376.57999"
                                    style="fill: currentcolor; fill-rule: evenodd; stroke: none; stroke-width: 0.26458332; stroke-linecap: square; stroke-miterlimit: 10;"
                                  />
                                  <path
                                    d="m 13.503462,7.43817 0,3.852778 -3.37361,1.868749 -0.0167,-4.713193 z"
                                    id="path914"
                                    inkscape:connector-curvature="0"
                                    inkscape:export-xdpi="376.57999"
                                    inkscape:export-ydpi="376.57999"
                                    style="fill: currentcolor; fill-rule: evenodd; stroke: none; stroke-width: 0.26458332; stroke-linecap: square; stroke-miterlimit: 10;"
                                  />
                                </g>
                              </g>
                            </svg>
                          </div>
                          <div
                            class="MuiBox-root css-sllbpf"
                          >
                            Namespace
                          </div>
                          <div
                            class="MuiBox-root css-0"
                          >
                            default
                          </div>
                        </div>
                      </div>
                    </div>
                    <div
                      aria-describedby="react-flow__node-desc-1"
                      class="react-flow__node react-flow__node-object selectable"
                      data-id="mock-id"
                      data-testid="rf__node-mock-id"
                      style="z-index: 0; transform: translate(48px,138px); pointer-events: all; visibility: visible; width: 220px; height: 70px;"
                    >
                      <div
                        class="css-1ky7h91"
                        role="button"
                        tabindex="0"
                      >
                        <div
                          class="react-flow__handle react-flow__handle-top nodrag nopan target connectable connectablestart connectableend connectionindicator"
                          data-handlepos="top"
                          data-id="1-mock-id-null-target"
                          data-nodeid="mock-id"
                          style="opacity: 0;"
                        />
                        <div
                          class="react-flow__handle react-flow__handle-bottom nodrag nopan source connectable connectablestart connectableend connectionindicator"
                          data-handlepos="bottom"
                          data-id="1-mock-id-null-source"
                          data-nodeid="mock-id"
                          style="opacity: 0;"
                        />
                        <div
                          class="css-1svjhx1"
                        />
                        <div
                          class="css-1irety8"
                        >
                          <div
                            class="MuiBox-root css-1igsrm0"
                          >
                            <svg
                              height="17.500378mm"
                              id="svg13826"
                              inkscape:version="0.91 r13725"
                              sodipodi:docname="pod.svg"
                              style="scale: 1.1; width: 100%; height: 100%;"
                              viewBox="0 0 18.035334 17.500378"
                              width="18.035334mm"
                              xmlns="http://www.w3.org/2000/svg"
                              xmlns:cc="http://creativecommons.org/ns#"
                              xmlns:dc="http://purl.org/dc/elements/1.1/"
                              xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
                              xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
                              xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
                              xmlns:svg="http://www.w3.org/2000/svg"
                            >
                              <defs
                                id="defs13820"
                              />
                              <sodipodi:namedview
                                bordercolor="#666666"
                                borderopacity="1"
                                fit-margin-bottom="0"
                                fit-margin-left="0"
                                fit-margin-right="0"
                                fit-margin-top="0"
                                id="base"
                                inkscape:current-layer="layer1"
                                inkscape:cx="-2.090004"
                                inkscape:cy="33.752239"
                                inkscape:document-units="mm"
                                inkscape:pageopacity="0"
                                inkscape:pageshadow="2"
                                inkscape:window-height="775"
                                inkscape:window-maximized="1"
                                inkscape:window-width="1440"
                                inkscape:window-x="0"
                                inkscape:window-y="1"
                                inkscape:zoom="8"
                                pagecolor="currentcolor"
                                showgrid="false"
                              />
                              <metadata
                                id="metadata13823"
                              >
                                <rdf:rdf>
                                  <cc:work
                                    rdf:about=""
                                  >
                                    <dc:format>
                                      image/svg+xml
                                    </dc:format>
                                    <dc:type
                                      rdf:resource="http://purl.org/dc/dcmitype/StillImage"
                                    />
                                    <dc:title />
                                  </cc:work>
                                </rdf:rdf>
                              </metadata>
                              <g
                                id="layer1"
                                inkscape:groupmode="layer"
                                inkscape:label="Calque 1"
                                transform="translate(-0.99262638,-1.174181)"
                              >
                                <g
                                  id="g70"
                                  transform="matrix(1.0148887,0,0,1.0148887,16.902146,-2.698726)"
                                >
                                  <path
                                    d="m -6.8492015,4.2724668 a 1.1191255,1.1099671 0 0 0 -0.4288818,0.1085303 l -5.8524037,2.7963394 a 1.1191255,1.1099671 0 0 0 -0.605524,0.7529759 l -1.443828,6.2812846 a 1.1191255,1.1099671 0 0 0 0.151943,0.851028 1.1191255,1.1099671 0 0 0 0.06362,0.08832 l 4.0508,5.036555 a 1.1191255,1.1099671 0 0 0 0.874979,0.417654 l 6.4961011,-0.0015 a 1.1191255,1.1099671 0 0 0 0.8749788,-0.416906 L 1.3818872,15.149453 A 1.1191255,1.1099671 0 0 0 1.5981986,14.210104 L 0.15212657,7.9288154 A 1.1191255,1.1099671 0 0 0 -0.45339794,7.1758396 L -6.3065496,4.3809971 A 1.1191255,1.1099671 0 0 0 -6.8492015,4.2724668 Z"
                                    id="path3055"
                                    inkscape:connector-curvature="0"
                                    inkscape:export-filename="new.png"
                                    inkscape:export-xdpi="250.55"
                                    inkscape:export-ydpi="250.55"
                                    style="fill: transparent; fill-opacity: 1; stroke: none; stroke-width: 0; stroke-miterlimit: 4; stroke-dasharray: none; stroke-opacity: 1;"
                                  />
                                  <path
                                    d="M -6.8523435,3.8176372 A 1.1814304,1.171762 0 0 0 -7.3044284,3.932904 l -6.1787426,2.9512758 a 1.1814304,1.171762 0 0 0 -0.639206,0.794891 l -1.523915,6.6308282 a 1.1814304,1.171762 0 0 0 0.160175,0.89893 1.1814304,1.171762 0 0 0 0.06736,0.09281 l 4.276094,5.317236 a 1.1814304,1.171762 0 0 0 0.92363,0.440858 l 6.8576188,-0.0015 a 1.1814304,1.171762 0 0 0 0.9236308,-0.44011 l 4.2745966,-5.317985 a 1.1814304,1.171762 0 0 0 0.228288,-0.990993 L 0.53894439,7.6775738 A 1.1814304,1.171762 0 0 0 -0.10026101,6.8834313 L -6.2790037,3.9321555 A 1.1814304,1.171762 0 0 0 -6.8523435,3.8176372 Z m 0.00299,0.4550789 a 1.1191255,1.1099671 0 0 1 0.5426517,0.1085303 l 5.85315169,2.7948425 A 1.1191255,1.1099671 0 0 1 0.15197811,7.9290648 L 1.598051,14.21035 a 1.1191255,1.1099671 0 0 1 -0.2163123,0.939348 l -4.0493032,5.037304 a 1.1191255,1.1099671 0 0 1 -0.8749789,0.416906 l -6.4961006,0.0015 a 1.1191255,1.1099671 0 0 1 -0.874979,-0.417652 l -4.0508,-5.036554 a 1.1191255,1.1099671 0 0 1 -0.06362,-0.08832 1.1191255,1.1099671 0 0 1 -0.151942,-0.851028 l 1.443827,-6.2812853 a 1.1191255,1.1099671 0 0 1 0.605524,-0.7529758 l 5.8524036,-2.7963395 a 1.1191255,1.1099671 0 0 1 0.4288819,-0.1085303 z"
                                    id="path3054-2-9"
                                    inkscape:connector-curvature="0"
                                    style="color: rgb(0, 0, 0); font-style: normal; font-variant: normal; font-weight: normal; font-stretch: normal; font-size: medium; line-height: normal; font-family: Sans; text-indent: 0; text-align: start; text-decoration: none; text-decoration-line: none; letter-spacing: normal; word-spacing: normal; text-transform: none; writing-mode: lr-tb; direction: ltr; baseline-shift: baseline; text-anchor: start; display: inline; overflow: visible; visibility: visible; fill: currentcolor; fill-opacity: 1; fill-rule: nonzero; stroke: none; stroke-width: 0; stroke-miterlimit: 4; stroke-dasharray: none; marker: none; enable-background: accumulate;"
                                  />
                                </g>
                                <g
                                  id="g3341"
                                  transform="translate(0.12766661,0.35147801)"
                                >
                                  <path
                                    d="M 6.2617914,7.036086 9.8826317,5.986087 13.503462,7.036086 9.8826317,8.086087 Z"
                                    id="path910"
                                    inkscape:connector-curvature="0"
                                    inkscape:export-xdpi="376.57999"
                                    inkscape:export-ydpi="376.57999"
                                    style="fill: currentcolor; fill-rule: evenodd; stroke: none; stroke-width: 0.26458332; stroke-linecap: square; stroke-miterlimit: 10;"
                                  />
                                  <path
                                    d="m 6.2617914,7.43817 0,3.852778 3.3736103,1.868749 0.0167,-4.713193 z"
                                    id="path912"
                                    inkscape:connector-curvature="0"
                                    inkscape:export-xdpi="376.57999"
                                    inkscape:export-ydpi="376.57999"
                                    style="fill: currentcolor; fill-rule: evenodd; stroke: none; stroke-width: 0.26458332; stroke-linecap: square; stroke-miterlimit: 10;"
                                  />
                                  <path
                                    d="m 13.503462,7.43817 0,3.852778 -3.37361,1.868749 -0.0167,-4.713193 z"
                                    id="path914"
                                    inkscape:connector-curvature="0"
                                    inkscape:export-xdpi="376.57999"
                                    inkscape:export-ydpi="376.57999"
                                    style="fill: currentcolor; fill-rule: evenodd; stroke: none; stroke-width: 0.26458332; stroke-linecap: square; stroke-miterlimit: 10;"
                                  />
                                </g>
                              </g>
                            </svg>
                          </div>
                          <div
                            class="css-1o5h0m"
                          >
                            <div
                              class="css-x2hlid"
                            >
                              Pod
                            </div>
                            <div
                              class="css-estfx9"
                            >
                              imagepullbackoff
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div
                      aria-describedby="react-flow__node-desc-1"
                      class="react-flow__node react-flow__node-object selectable"
                      data-id="custom-node"
                      data-testid="rf__node-custom-node"
                      style="z-index: 0; transform: translate(24px,252px); pointer-events: all; visibility: visible; width: 220px; height: 70px;"
                    >
                      <div
                        class="css-1ky7h91"
                        role="button"
                        tabindex="0"
                      >
                        <div
                          class="react-flow__handle react-flow__handle-top nodrag nopan target connectable connectablestart connectableend connectionindicator"
                          data-handlepos="top"
                          data-id="1-custom-node-null-target"
                          data-nodeid="custom-node"
                          style="opacity: 0;"
                        />
                        <div
                          class="react-flow__handle react-flow__handle-bottom nodrag nopan source connectable connectablestart connectableend connectionindicator"
                          data-handlepos="bottom"
                          data-id="1-custom-node-null-source"
                          data-nodeid="custom-node"
                          style="opacity: 0;"
                        />
                        <div
                          class="css-1irety8"
                        >
                          <div
                            class="css-1o5h0m"
                          >
                            <div
                              class="css-x2hlid"
                            >
                              Node Subtitle
                            </div>
                            <div
                              class="css-estfx9"
                            >
                              Node Label
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div
                      aria-describedby="react-flow__node-desc-1"
                      class="react-flow__node react-flow__node-object selectable"
                      data-id="custom-node-with-icon"
                      data-testid="rf__node-custom-node-with-icon"
                      style="z-index: 0; transform: translate(264px,252px); pointer-events: all; visibility: visible; width: 220px; height: 70px;"
                    >
                      <div
                        class="css-1ky7h91"
                        role="button"
                        tabindex="0"
                      >
                        <div
                          class="react-flow__handle react-flow__handle-top nodrag nopan target connectable connectablestart connectableend connectionindicator"
                          data-handlepos="top"
                          data-id="1-custom-node-with-icon-null-target"
                          data-nodeid="custom-node-with-icon"
                          style="opacity: 0;"
                        />
                        <div
                          class="react-flow__handle react-flow__handle-bottom nodrag nopan source connectable connectablestart connectableend connectionindicator"
                          data-handlepos="bottom"
                          data-id="1-custom-node-with-icon-null-source"
                          data-nodeid="custom-node-with-icon"
                          style="opacity: 0;"
                        />
                        <div
                          class="css-1irety8"
                        >
                          <div
                            class="css-1o5h0m"
                          >
                            <div
                              class="css-x2hlid"
                            >
                              Node Subtitle
                            </div>
                            <div
                              class="css-estfx9"
                            >
                              Node with an icon
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div
                      aria-describedby="react-flow__node-desc-1"
                      class="react-flow__node react-flow__node-object selectable"
                      data-id="custom-node-with-details"
                      data-testid="rf__node-custom-node-with-details"
                      style="z-index: 0; transform: translate(24px,342px); pointer-events: all; visibility: visible; width: 220px; height: 70px;"
                    >
                      <div
                        class="css-1ky7h91"
                        role="button"
                        tabindex="0"
                      >
                        <div
                          class="react-flow__handle react-flow__handle-top nodrag nopan target connectable connectablestart connectableend connectionindicator"
                          data-handlepos="top"
                          data-id="1-custom-node-with-details-null-target"
                          data-nodeid="custom-node-with-details"
                          style="opacity: 0;"
                        />
                        <div
                          class="react-flow__handle react-flow__handle-bottom nodrag nopan source connectable connectablestart connectableend connectionindicator"
                          data-handlepos="bottom"
                          data-id="1-custom-node-with-details-null-source"
                          data-nodeid="custom-node-with-details"
                          style="opacity: 0;"
                        />
                        <div
                          class="css-1irety8"
                        >
                          <div
                            class="css-1o5h0m"
                          >
                            <div
                              class="css-x2hlid"
                            >
                              Click to see custom details
                            </div>
                            <div
                              class="css-estfx9"
                            >
                              Node with custom details
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div
                    class="react-flow__viewport-portal"
                  />
                </div>
              </div>
            </div>
            <svg
              class="react-flow__background"
              data-testid="rf__background"
              style="color: rgba(0, 0, 0, 0.12); position: absolute; width: 100%; height: 100%; top: 0px; left: 0px;"
            >
              <pattern
                height="20"
                id="pattern-1"
                patternTransform="translate(-11,-11)"
                patternUnits="userSpaceOnUse"
                width="20"
                x="10"
                y="16"
              >
                <circle
                  class="react-flow__background-pattern dots"
                  cx="0.5"
                  cy="0.5"
                  r="0.5"
                />
              </pattern>
              <rect
                fill="url(#pattern-1)"
                height="100%"
                width="100%"
                x="0"
                y="0"
              />
            </svg>
            <div
              aria-label="React Flow controls"
              class="react-flow__panel react-flow__controls vertical bottom left"
              data-testid="rf__controls"
              style="pointer-events: all;"
            >
              <div
                class="MuiBox-root css-1u72um6"
              >
                <div
                  aria-label="Vertical button group"
                  class="MuiButtonGroup-root MuiButtonGroup-contained MuiButtonGroup-vertical css-126jwi2-MuiButtonGroup-root"
                  role="group"
                >
                  <button
                    class="MuiButtonBase-root MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeMedium MuiButton-containedSizeMedium MuiButton-colorPrimary MuiButtonGroup-grouped MuiButtonGroup-groupedVertical MuiButtonGroup-groupedContained MuiButtonGroup-groupedContainedVertical MuiButtonGroup-groupedContainedPrimary MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeMedium MuiButton-containedSizeMedium MuiButton-colorPrimary MuiButtonGroup-grouped MuiButtonGroup-groupedVertical MuiButtonGroup-groupedContained MuiButtonGroup-groupedContainedVertical MuiButtonGroup-groupedContainedPrimary MuiButtonGroup-firstButton css-14951ov-MuiButtonBase-root-MuiButton-root"
                    tabindex="0"
                    title="Zoom in"
                    type="button"
                  >
                    <span
                      class="MuiTouchRipple-root css-8je8zh-MuiTouchRipple-root"
                    />
                  </button>
                  <button
                    class="MuiButtonBase-root MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeMedium MuiButton-containedSizeMedium MuiButton-colorPrimary MuiButtonGroup-grouped MuiButtonGroup-groupedVertical MuiButtonGroup-groupedContained MuiButtonGroup-groupedContainedVertical MuiButtonGroup-groupedContainedPrimary MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeMedium MuiButton-containedSizeMedium MuiButton-colorPrimary MuiButtonGroup-grouped MuiButtonGroup-groupedVertical MuiButtonGroup-groupedContained MuiButtonGroup-groupedContainedVertical MuiButtonGroup-groupedContainedPrimary MuiButtonGroup-lastButton css-14951ov-MuiButtonBase-root-MuiButton-root"
                    tabindex="0"
                    title="Zoom out"
                    type="button"
                  >
                    <span
                      class="MuiTouchRipple-root css-8je8zh-MuiTouchRipple-root"
                    />
                  </button>
                </div>
                <button
                  class="MuiButtonBase-root MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeMedium MuiButton-containedSizeMedium MuiButton-colorPrimary MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeMedium MuiButton-containedSizeMedium MuiButton-colorPrimary css-14951ov-MuiButtonBase-root-MuiButton-root"
                  tabindex="0"
                  title="Fit to screen"
                  type="button"
                >
                  <span
                    class="MuiTouchRipple-root css-8je8zh-MuiTouchRipple-root"
                  />
                </button>
                <button
                  class="MuiButtonBase-root MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeMedium MuiButton-containedSizeMedium MuiButton-colorPrimary MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeMedium MuiButton-containedSizeMedium MuiButton-colorPrimary css-14951ov-MuiButtonBase-root-MuiButton-root"
                  tabindex="0"
                  title="Zoom to 100%"
                  type="button"
                >
                  100%
                  <span
                    class="MuiTouchRipple-root css-8je8zh-MuiTouchRipple-root"
                  />
                </button>
              </div>
            </div>
            <div
              class="react-flow__panel top left"
              style="pointer-events: all;"
            />
            <div
              class="react-flow__panel react-flow__attribution bottom right"
              data-message="Please only hide this attribution when you are subscribed to React Flow Pro: https://pro.reactflow.dev"
              style="pointer-events: all;"
            >
              <a
                aria-label="React Flow attribution"
                href="https://reactflow.dev"
                rel="noopener noreferrer"
                target="_blank"
              >
                React Flow
              </a>
            </div>
            <div
              id="react-flow__node-desc-1"
              style="display: none;"
            >
              Press enter or space to select a node.
              You can then use the arrow keys to move the node around.
               Press delete to remove it and escape to cancel.
               
            </div>
            <div
              id="react-flow__edge-desc-1"
              style="display: none;"
            >
              Press enter or space to select an edge. You can then press delete to remove it or escape to cancel.
            </div>
            <div
              aria-atomic="true"
              aria-live="assertive"
              id="react-flow__aria-live-1"
              style="position: absolute; width: 1px; height: 1px; margin: -1px; border: 0px; padding: 0px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); clip-path: inset(100%);"
            />
          </div>
        </div>
      </div>
    </div>
    ;
  </div>
</body>