<body>
  <div
    aria-hidden="true"
  >
    <div />
  </div>
  <div
    class="MuiDialog-root MuiModal-root css-zw3mfo-MuiModal-root-MuiDialog-root"
    role="presentation"
  >
    <div
      aria-hidden="true"
      class="MuiBackdrop-root MuiModal-backdrop css-yiavyu-MuiBackdrop-root-MuiDialog-backdrop"
      style="opacity: 1; webkit-transition: opacity 225ms cubic-bezier(0.4, 0, 0.2, 1) 0ms; transition: opacity 225ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;"
    />
    <div
      data-testid="sentinelStart"
      tabindex="0"
    />
    <div
      class="MuiDialog-container MuiDialog-scrollPaper css-hz1bth-MuiDialog-container"
      role="presentation"
      style="opacity: 1; webkit-transition: opacity 225ms cubic-bezier(0.4, 0, 0.2, 1) 0ms; transition: opacity 225ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;"
      tabindex="-1"
    >
      <div
        aria-describedby="alert-dialog-description"
        aria-labelledby="alert-dialog-title"
        class="MuiPaper-root MuiPaper-outlined MuiPaper-rounded MuiDialog-paper MuiDialog-paperScrollPaper MuiDialog-paperWidthSm css-1708vl9-MuiPaper-root-MuiDialog-paper"
        role="dialog"
      >
        <h2
          class="MuiTypography-root MuiTypography-h6 MuiDialogTitle-root css-8yphvn-MuiTypography-root-MuiDialogTitle-root"
          id="alert-dialog-title"
          style="display: flex;"
        >
          <div
            class="MuiGrid-root MuiGrid-container css-9cyib4-MuiGrid-root"
          >
            <div
              class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
            >
              <h1
                class="MuiTypography-root MuiTypography-h1 css-1kazmbo-MuiTypography-root"
                style="font-size: 1.25rem; font-weight: 500; line-height: 1.6;"
              >
                A fine title
              </h1>
            </div>
          </div>
        </h2>
        <div
          class="MuiDialogContent-root css-9hw3b4-MuiDialogContent-root"
          tabindex="-1"
        >
          <div
            class="MuiTypography-root MuiDialogContentText-root MuiTypography-body1 MuiDialogContentText-root css-1tmyuhs-MuiTypography-root-MuiDialogContentText-root"
            id="alert-dialog-description"
          >
            A really good description.
          </div>
        </div>
        <div
          class="MuiDialogActions-root MuiDialogActions-spacing css-knqc4i-MuiDialogActions-root"
        >
          <button
            aria-label="cancel-button"
            class="MuiButtonBase-root MuiButton-root MuiButton-contained MuiButton-containedSecondary MuiButton-sizeMedium MuiButton-containedSizeMedium MuiButton-colorSecondary MuiButton-disableElevation MuiButton-root MuiButton-contained MuiButton-containedSecondary MuiButton-sizeMedium MuiButton-containedSizeMedium MuiButton-colorSecondary MuiButton-disableElevation css-14f6w6s-MuiButtonBase-root-MuiButton-root"
            tabindex="0"
            type="button"
          >
            No
            <span
              class="MuiTouchRipple-root css-8je8zh-MuiTouchRipple-root"
            />
          </button>
          <button
            aria-label="confirm-button"
            class="MuiButtonBase-root MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeMedium MuiButton-containedSizeMedium MuiButton-colorPrimary MuiButton-disableElevation MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeMedium MuiButton-containedSizeMedium MuiButton-colorPrimary MuiButton-disableElevation css-gn8fa3-MuiButtonBase-root-MuiButton-root"
            tabindex="0"
            type="button"
          >
            Yes
            <span
              class="MuiTouchRipple-root css-8je8zh-MuiTouchRipple-root"
            />
          </button>
        </div>
      </div>
    </div>
    <div
      data-testid="sentinelEnd"
      tabindex="0"
    />
  </div>
</body>