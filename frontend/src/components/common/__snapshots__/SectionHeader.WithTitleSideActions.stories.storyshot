<body>
  <div>
     
    <div
      class="MuiGrid-root MuiGrid-container MuiGrid-spacing-xs-2 css-1ts0dnm-MuiGrid-root"
    >
      <div
        class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
      >
        <div
          class="MuiBox-root css-70qvj9"
        >
          <h3
            class="MuiTypography-root MuiTypography-h3 MuiTypography-noWrap css-omosr2-MuiTypography-root"
          >
            Section with Title-Side Info
          </h3>
          <div
            class="MuiBox-root css-ldp2l3"
          >
            <div
              class="MuiChip-root MuiChip-filled MuiChip-sizeSmall MuiChip-colorSecondary MuiChip-filledSecondary css-ce6uou-MuiChip-root"
            >
              <span
                class="MuiChip-label MuiChip-labelSmall css-wjsjww-MuiChip-label"
              >
                Beta
              </span>
            </div>
            <button
              aria-label="info"
              class="MuiButtonBase-root MuiIconButton-root MuiIconButton-sizeSmall css-qsifm6-MuiButtonBase-root-MuiIconButton-root"
              tabindex="0"
              type="button"
            >
              <span
                class="MuiTouchRipple-root css-8je8zh-MuiTouchRipple-root"
              />
            </button>
          </div>
        </div>
      </div>
      <div
        class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
      >
        <div
          class="MuiGrid-root MuiGrid-container MuiGrid-item css-ztq4zc-MuiGrid-root"
        >
          <div
            class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
          >
            <button
              class="MuiButtonBase-root MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeSmall MuiButton-outlinedSizeSmall MuiButton-colorPrimary MuiButton-disableElevation MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeSmall MuiButton-outlinedSizeSmall MuiButton-colorPrimary MuiButton-disableElevation css-irzb5y-MuiButtonBase-root-MuiButton-root"
              tabindex="0"
              type="button"
            >
              Configure
              <span
                class="MuiTouchRipple-root css-8je8zh-MuiTouchRipple-root"
              />
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</body>