<body>
  <div>
    <div
      class="MuiBox-root css-j1fy4m"
    >
      <div
        class="MuiGrid-root MuiGrid-container MuiGrid-spacing-xs-2 css-1ts0dnm-MuiGrid-root"
      >
        <div
          class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
        >
          <div
            class="MuiBox-root css-70qvj9"
          >
            <h2
              class="MuiTypography-root MuiTypography-h2 MuiTypography-noWrap css-m5vcfd-MuiTypography-root"
            >
              Events
            </h2>
            <div
              class="MuiBox-root css-ldp2l3"
            />
          </div>
        </div>
      </div>
      <div
        class="MuiBox-root css-1txv3mw"
      >
        <div
          class="MuiPaper-root MuiPaper-outlined MuiPaper-rounded MuiTableContainer-root css-onzayo-MuiPaper-root-MuiTableContainer-root"
        >
          <table
            class="MuiTable-root css-r7i92b-MuiTable-root"
          >
            <thead
              class="MuiTableHead-root css-15wwp11-MuiTableHead-root"
            >
              <tr
                class="MuiTableRow-root MuiTableRow-head css-13jktim-MuiTableRow-root"
              >
                <th
                  class="MuiTableCell-root MuiTableCell-head MuiTableCell-sizeSmall css-l4jzay-MuiTableCell-root"
                  scope="col"
                >
                  Type
                </th>
                <th
                  class="MuiTableCell-root MuiTableCell-head MuiTableCell-sizeSmall css-l4jzay-MuiTableCell-root"
                  scope="col"
                >
                  Reason
                </th>
                <th
                  class="MuiTableCell-root MuiTableCell-head MuiTableCell-sizeSmall css-l4jzay-MuiTableCell-root"
                  scope="col"
                >
                  From
                </th>
                <th
                  class="MuiTableCell-root MuiTableCell-head MuiTableCell-sizeSmall css-l4jzay-MuiTableCell-root"
                  scope="col"
                >
                  Message
                </th>
                <th
                  class="MuiTableCell-root MuiTableCell-head MuiTableCell-sizeSmall css-d9iroo-MuiTableCell-root"
                  scope="col"
                >
                  Age
                  <button
                    aria-label="sort swap"
                    class="MuiButtonBase-root MuiIconButton-root MuiIconButton-sizeSmall css-hvz71z-MuiButtonBase-root-MuiIconButton-root"
                    tabindex="0"
                    type="button"
                  >
                    <span
                      class="MuiTouchRipple-root css-8je8zh-MuiTouchRipple-root"
                    />
                  </button>
                </th>
              </tr>
            </thead>
            <tbody
              class="MuiTableBody-root css-apqrd9-MuiTableBody-root"
            >
              <tr
                class="MuiTableRow-root css-13jktim-MuiTableRow-root"
              >
                <td
                  class="MuiTableCell-root MuiTableCell-body MuiTableCell-sizeSmall css-oh2q4q-MuiTableCell-root"
                >
                  Normal
                </td>
                <td
                  class="MuiTableCell-root MuiTableCell-body MuiTableCell-sizeSmall css-oh2q4q-MuiTableCell-root"
                >
                  Scheduled
                </td>
                <td
                  class="MuiTableCell-root MuiTableCell-body MuiTableCell-sizeSmall css-oh2q4q-MuiTableCell-root"
                >
                  default-scheduler
                </td>
                <td
                  class="MuiTableCell-root MuiTableCell-body MuiTableCell-sizeSmall css-oh2q4q-MuiTableCell-root"
                >
                  <div
                    class="MuiBox-root css-k008qs"
                  >
                    <label
                      id="event-uid-1"
                      style="word-break: break-all; white-space: normal;"
                    >
                      Successfully assigned default/test-pod-for-events to worker-node-1
                    </label>
                  </div>
                </td>
                <td
                  class="MuiTableCell-root MuiTableCell-body MuiTableCell-sizeSmall css-oh2q4q-MuiTableCell-root"
                >
                  <p
                    aria-label="2024-02-14T23:55:00.000Z"
                    class="MuiTypography-root MuiTypography-body1 css-1d0cpfm-MuiTypography-root"
                    data-mui-internal-clone-element="true"
                  >
                    3mo
                  </p>
                </td>
              </tr>
              <tr
                class="MuiTableRow-root css-13jktim-MuiTableRow-root"
              >
                <td
                  class="MuiTableCell-root MuiTableCell-body MuiTableCell-sizeSmall css-oh2q4q-MuiTableCell-root"
                >
                  Normal
                </td>
                <td
                  class="MuiTableCell-root MuiTableCell-body MuiTableCell-sizeSmall css-oh2q4q-MuiTableCell-root"
                >
                  Pulled
                </td>
                <td
                  class="MuiTableCell-root MuiTableCell-body MuiTableCell-sizeSmall css-oh2q4q-MuiTableCell-root"
                >
                  kubelet
                </td>
                <td
                  class="MuiTableCell-root MuiTableCell-body MuiTableCell-sizeSmall css-oh2q4q-MuiTableCell-root"
                >
                  <div
                    class="MuiBox-root css-k008qs"
                  >
                    <label
                      id="event-uid-2"
                      style="word-break: break-all; white-space: normal;"
                    >
                      Container image "nginx:latest" already present on machine
                    </label>
                  </div>
                </td>
                <td
                  class="MuiTableCell-root MuiTableCell-body MuiTableCell-sizeSmall css-oh2q4q-MuiTableCell-root"
                >
                  <p
                    aria-label="2024-02-14T23:58:00.000Z"
                    class="MuiTypography-root MuiTypography-body1 css-1d0cpfm-MuiTypography-root"
                    data-mui-internal-clone-element="true"
                  >
                    3mo
                  </p>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</body>