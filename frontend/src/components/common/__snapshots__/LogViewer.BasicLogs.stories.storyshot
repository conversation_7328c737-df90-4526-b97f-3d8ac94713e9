<body>
  <div
    aria-hidden="true"
  />
  <div
    class="MuiDialog-root MuiModal-root css-zw3mfo-MuiModal-root-MuiDialog-root"
    role="presentation"
  >
    <div
      aria-hidden="true"
      class="MuiBackdrop-root MuiModal-backdrop css-yiavyu-MuiBackdrop-root-MuiDialog-backdrop"
      style="opacity: 1; webkit-transition: opacity 225ms cubic-bezier(0.4, 0, 0.2, 1) 0ms; transition: opacity 225ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;"
    />
    <div
      data-testid="sentinelStart"
      tabindex="0"
    />
    <div
      class="MuiDialog-container MuiDialog-scrollPaper css-hz1bth-MuiDialog-container"
      role="presentation"
      style="opacity: 1; webkit-transition: opacity 225ms cubic-bezier(0.4, 0, 0.2, 1) 0ms; transition: opacity 225ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;"
      tabindex="-1"
    >
      <div
        aria-labelledby=":mock-test-id:"
        class="MuiPaper-root MuiPaper-elevation MuiPaper-rounded MuiPaper-elevation24 MuiDialog-paper MuiDialog-paperScrollPaper MuiDialog-paperWidthLg MuiDialog-paperFullWidth css-1iou21e-MuiPaper-root-MuiDialog-paper"
        role="dialog"
      >
        <h2
          class="MuiTypography-root MuiTypography-h6 MuiDialogTitle-root css-8yphvn-MuiTypography-root-MuiDialogTitle-root"
          id=":mock-test-id:"
          style="display: flex;"
        >
          <div
            class="MuiGrid-root MuiGrid-container css-9cyib4-MuiGrid-root"
          >
            <div
              class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
            >
              <h1
                class="MuiTypography-root MuiTypography-h1 css-1kazmbo-MuiTypography-root"
                style="font-size: 1.25rem; font-weight: 500; line-height: 1.6;"
              >
                Basic Logs
              </h1>
            </div>
            <div
              class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
            >
              <div
                class="MuiBox-root css-0"
              >
                <button
                  aria-label="Toggle fullscreen"
                  class="MuiButtonBase-root MuiIconButton-root MuiIconButton-sizeMedium css-whz9ym-MuiButtonBase-root-MuiIconButton-root"
                  data-mui-internal-clone-element="true"
                  tabindex="0"
                  type="button"
                >
                  <span
                    class="MuiTouchRipple-root css-8je8zh-MuiTouchRipple-root"
                  />
                </button>
                <button
                  aria-label="Close"
                  class="MuiButtonBase-root MuiIconButton-root MuiIconButton-sizeMedium css-whz9ym-MuiButtonBase-root-MuiIconButton-root"
                  data-mui-internal-clone-element="true"
                  tabindex="0"
                  type="button"
                >
                  <span
                    class="MuiTouchRipple-root css-8je8zh-MuiTouchRipple-root"
                  />
                </button>
              </div>
            </div>
          </div>
        </h2>
        <div
          class="MuiDialogContent-root css-4kzd3n-MuiDialogContent-root"
        >
          <div
            class="MuiGrid-root MuiGrid-container MuiGrid-wrap-xs-nowrap css-1c9lqjq-MuiGrid-root"
          >
            <div
              class="MuiGrid-root MuiGrid-container MuiGrid-item MuiGrid-spacing-xs-1 css-v8g4am-MuiGrid-root"
            />
            <div
              class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-true css-1z0dvx8-MuiGrid-root"
            >
              <button
                aria-label="Find"
                class="MuiButtonBase-root MuiIconButton-root MuiIconButton-sizeMedium css-whz9ym-MuiButtonBase-root-MuiIconButton-root"
                data-mui-internal-clone-element="true"
                tabindex="0"
                type="button"
              >
                <span
                  class="MuiTouchRipple-root css-8je8zh-MuiTouchRipple-root"
                />
              </button>
            </div>
            <div
              class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-true css-1z0dvx8-MuiGrid-root"
            >
              <button
                aria-label="Clear"
                class="MuiButtonBase-root MuiIconButton-root MuiIconButton-sizeMedium css-whz9ym-MuiButtonBase-root-MuiIconButton-root"
                data-mui-internal-clone-element="true"
                tabindex="0"
                type="button"
              >
                <span
                  class="MuiTouchRipple-root css-8je8zh-MuiTouchRipple-root"
                />
              </button>
            </div>
            <div
              class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-true css-1z0dvx8-MuiGrid-root"
            >
              <button
                aria-label="Download"
                class="MuiButtonBase-root MuiIconButton-root MuiIconButton-sizeMedium css-whz9ym-MuiButtonBase-root-MuiIconButton-root"
                data-mui-internal-clone-element="true"
                tabindex="0"
                type="button"
              >
                <span
                  class="MuiTouchRipple-root css-8je8zh-MuiTouchRipple-root"
                />
              </button>
            </div>
          </div>
          <div
            class="MuiBox-root css-1j1vsa7"
          >
            <div
              id="xterm-container"
              style="flex: 1; display: flex; flex-direction: column-reverse;"
            >
              <div
                class="terminal xterm xterm-dom-renderer-owner"
                dir="ltr"
              >
                <div
                  class="xterm-viewport"
                  style="background-color: rgb(0, 0, 0);"
                >
                  <div
                    class="xterm-scroll-area"
                  />
                </div>
                <div
                  class="xterm-screen"
                  style="width: 0px; height: 0px;"
                >
                  <div
                    class="xterm-helpers"
                  >
                    <textarea
                      aria-label="Terminal input"
                      aria-multiline="false"
                      autocapitalize="off"
                      autocorrect="off"
                      class="xterm-helper-textarea"
                      spellcheck="false"
                      style="left: 0px; top: 0px; width: 0px; height: 0px; line-height: 0px; z-index: -5;"
                      tabindex="0"
                    />
                    <span
                      aria-hidden="true"
                      class="xterm-char-measure-element"
                      style="white-space: pre; font-kerning: none; font-family: courier-new, courier, monospace; font-size: 15px;"
                    >
                      WWWWWWWWWWWWWWWWWWWWWWWWWWWWWWWW
                    </span>
                    <div
                      class="composition-view"
                    />
                    <div
                      aria-hidden="true"
                      class="xterm-width-cache-measure-container"
                      style="white-space: pre; font-kerning: none; font-family: courier-new, courier, monospace; font-size: 15px;"
                    >
                      <span
                        class="xterm-char-measure-element"
                        style="font-weight: normal;"
                      >
                                                        
                      </span>
                      <span
                        class="xterm-char-measure-element"
                        style="font-weight: bold;"
                      />
                      <span
                        class="xterm-char-measure-element"
                        style="font-style: italic; font-weight: normal;"
                      />
                      <span
                        class="xterm-char-measure-element"
                        style="font-weight: bold; font-style: italic;"
                      />
                    </div>
                  </div>
                  <style>
                    .xterm-dom-renderer-owner-1 .xterm-rows span { display: inline-block; height: 100%; vertical-align: top;}
                  </style>
                  <style>
                    .xterm-dom-renderer-owner-1 .xterm-rows { color: #ffffff; font-family: courier-new, courier, monospace; font-size: 15px; font-kerning: none; white-space: pre}.xterm-dom-renderer-owner-1 .xterm-rows .xterm-dim { color: #ffffff80;}.xterm-dom-renderer-owner-1 span:not(.xterm-bold) { font-weight: normal;}.xterm-dom-renderer-owner-1 span.xterm-bold { font-weight: bold;}.xterm-dom-renderer-owner-1 span.xterm-italic { font-style: italic;}@keyframes blink_underline_1 { 50% {  border-bottom-style: hidden; }}@keyframes blink_bar_1 { 50% {  box-shadow: none; }}@keyframes blink_block_1 { 0% {  background-color: #ffffff;  color: #000000; } 50% {  background-color: inherit;  color: #ffffff; }}.xterm-dom-renderer-owner-1 .xterm-rows.xterm-focus .xterm-cursor.xterm-cursor-blink.xterm-cursor-underline { animation: blink_underline_1 1s step-end infinite;}.xterm-dom-renderer-owner-1 .xterm-rows.xterm-focus .xterm-cursor.xterm-cursor-blink.xterm-cursor-bar { animation: blink_bar_1 1s step-end infinite;}.xterm-dom-renderer-owner-1 .xterm-rows.xterm-focus .xterm-cursor.xterm-cursor-blink.xterm-cursor-block { animation: blink_block_1 1s step-end infinite;}.xterm-dom-renderer-owner-1 .xterm-rows .xterm-cursor.xterm-cursor-block { background-color: #ffffff; color: #000000;}.xterm-dom-renderer-owner-1 .xterm-rows .xterm-cursor.xterm-cursor-block:not(.xterm-cursor-blink) { background-color: #ffffff !important; color: #000000 !important;}.xterm-dom-renderer-owner-1 .xterm-rows .xterm-cursor.xterm-cursor-outline { outline: 1px solid #ffffff; outline-offset: -1px;}.xterm-dom-renderer-owner-1 .xterm-rows .xterm-cursor.xterm-cursor-bar { box-shadow: 1px 0 0 #ffffff inset;}.xterm-dom-renderer-owner-1 .xterm-rows .xterm-cursor.xterm-cursor-underline { border-bottom: 1px #ffffff; border-bottom-style: solid; height: calc(100% - 1px);}.xterm-dom-renderer-owner-1 .xterm-selection { position: absolute; top: 0; left: 0; z-index: 1; pointer-events: none;}.xterm-dom-renderer-owner-1.focus .xterm-selection div { position: absolute; background-color: #4d4d4d;}.xterm-dom-renderer-owner-1 .xterm-selection div { position: absolute; background-color: #4d4d4d;}.xterm-dom-renderer-owner-1 .xterm-fg-0 { color: #2e3436; }.xterm-dom-renderer-owner-1 .xterm-fg-0.xterm-dim { color: #2e343680; }.xterm-dom-renderer-owner-1 .xterm-bg-0 { background-color: #2e3436; }.xterm-dom-renderer-owner-1 .xterm-fg-1 { color: #cc0000; }.xterm-dom-renderer-owner-1 .xterm-fg-1.xterm-dim { color: #cc000080; }.xterm-dom-renderer-owner-1 .xterm-bg-1 { background-color: #cc0000; }.xterm-dom-renderer-owner-1 .xterm-fg-2 { color: #4e9a06; }.xterm-dom-renderer-owner-1 .xterm-fg-2.xterm-dim { color: #4e9a0680; }.xterm-dom-renderer-owner-1 .xterm-bg-2 { background-color: #4e9a06; }.xterm-dom-renderer-owner-1 .xterm-fg-3 { color: #c4a000; }.xterm-dom-renderer-owner-1 .xterm-fg-3.xterm-dim { color: #c4a00080; }.xterm-dom-renderer-owner-1 .xterm-bg-3 { background-color: #c4a000; }.xterm-dom-renderer-owner-1 .xterm-fg-4 { color: #3465a4; }.xterm-dom-renderer-owner-1 .xterm-fg-4.xterm-dim { color: #3465a480; }.xterm-dom-renderer-owner-1 .xterm-bg-4 { background-color: #3465a4; }.xterm-dom-renderer-owner-1 .xterm-fg-5 { color: #75507b; }.xterm-dom-renderer-owner-1 .xterm-fg-5.xterm-dim { color: #75507b80; }.xterm-dom-renderer-owner-1 .xterm-bg-5 { background-color: #75507b; }.xterm-dom-renderer-owner-1 .xterm-fg-6 { color: #06989a; }.xterm-dom-renderer-owner-1 .xterm-fg-6.xterm-dim { color: #06989a80; }.xterm-dom-renderer-owner-1 .xterm-bg-6 { background-color: #06989a; }.xterm-dom-renderer-owner-1 .xterm-fg-7 { color: #d3d7cf; }.xterm-dom-renderer-owner-1 .xterm-fg-7.xterm-dim { color: #d3d7cf80; }.xterm-dom-renderer-owner-1 .xterm-bg-7 { background-color: #d3d7cf; }.xterm-dom-renderer-owner-1 .xterm-fg-8 { color: #555753; }.xterm-dom-renderer-owner-1 .xterm-fg-8.xterm-dim { color: #55575380; }.xterm-dom-renderer-owner-1 .xterm-bg-8 { background-color: #555753; }.xterm-dom-renderer-owner-1 .xterm-fg-9 { color: #ef2929; }.xterm-dom-renderer-owner-1 .xterm-fg-9.xterm-dim { color: #ef292980; }.xterm-dom-renderer-owner-1 .xterm-bg-9 { background-color: #ef2929; }.xterm-dom-renderer-owner-1 .xterm-fg-10 { color: #8ae234; }.xterm-dom-renderer-owner-1 .xterm-fg-10.xterm-dim { color: #8ae23480; }.xterm-dom-renderer-owner-1 .xterm-bg-10 { background-color: #8ae234; }.xterm-dom-renderer-owner-1 .xterm-fg-11 { color: #fce94f; }.xterm-dom-renderer-owner-1 .xterm-fg-11.xterm-dim { color: #fce94f80; }.xterm-dom-renderer-owner-1 .xterm-bg-11 { background-color: #fce94f; }.xterm-dom-renderer-owner-1 .xterm-fg-12 { color: #729fcf; }.xterm-dom-renderer-owner-1 .xterm-fg-12.xterm-dim { color: #729fcf80; }.xterm-dom-renderer-owner-1 .xterm-bg-12 { background-color: #729fcf; }.xterm-dom-renderer-owner-1 .xterm-fg-13 { color: #ad7fa8; }.xterm-dom-renderer-owner-1 .xterm-fg-13.xterm-dim { color: #ad7fa880; }.xterm-dom-renderer-owner-1 .xterm-bg-13 { background-color: #ad7fa8; }.xterm-dom-renderer-owner-1 .xterm-fg-14 { color: #34e2e2; }.xterm-dom-renderer-owner-1 .xterm-fg-14.xterm-dim { color: #34e2e280; }.xterm-dom-renderer-owner-1 .xterm-bg-14 { background-color: #34e2e2; }.xterm-dom-renderer-owner-1 .xterm-fg-15 { color: #eeeeec; }.xterm-dom-renderer-owner-1 .xterm-fg-15.xterm-dim { color: #eeeeec80; }.xterm-dom-renderer-owner-1 .xterm-bg-15 { background-color: #eeeeec; }.xterm-dom-renderer-owner-1 .xterm-fg-16 { color: #000000; }.xterm-dom-renderer-owner-1 .xterm-fg-16.xterm-dim { color: #00000080; }.xterm-dom-renderer-owner-1 .xterm-bg-16 { background-color: #000000; }.xterm-dom-renderer-owner-1 .xterm-fg-17 { color: #00005f; }.xterm-dom-renderer-owner-1 .xterm-fg-17.xterm-dim { color: #00005f80; }.xterm-dom-renderer-owner-1 .xterm-bg-17 { background-color: #00005f; }.xterm-dom-renderer-owner-1 .xterm-fg-18 { color: #000087; }.xterm-dom-renderer-owner-1 .xterm-fg-18.xterm-dim { color: #00008780; }.xterm-dom-renderer-owner-1 .xterm-bg-18 { background-color: #000087; }.xterm-dom-renderer-owner-1 .xterm-fg-19 { color: #0000af; }.xterm-dom-renderer-owner-1 .xterm-fg-19.xterm-dim { color: #0000af80; }.xterm-dom-renderer-owner-1 .xterm-bg-19 { background-color: #0000af; }.xterm-dom-renderer-owner-1 .xterm-fg-20 { color: #0000d7; }.xterm-dom-renderer-owner-1 .xterm-fg-20.xterm-dim { color: #0000d780; }.xterm-dom-renderer-owner-1 .xterm-bg-20 { background-color: #0000d7; }.xterm-dom-renderer-owner-1 .xterm-fg-21 { color: #0000ff; }.xterm-dom-renderer-owner-1 .xterm-fg-21.xterm-dim { color: #0000ff80; }.xterm-dom-renderer-owner-1 .xterm-bg-21 { background-color: #0000ff; }.xterm-dom-renderer-owner-1 .xterm-fg-22 { color: #005f00; }.xterm-dom-renderer-owner-1 .xterm-fg-22.xterm-dim { color: #005f0080; }.xterm-dom-renderer-owner-1 .xterm-bg-22 { background-color: #005f00; }.xterm-dom-renderer-owner-1 .xterm-fg-23 { color: #005f5f; }.xterm-dom-renderer-owner-1 .xterm-fg-23.xterm-dim { color: #005f5f80; }.xterm-dom-renderer-owner-1 .xterm-bg-23 { background-color: #005f5f; }.xterm-dom-renderer-owner-1 .xterm-fg-24 { color: #005f87; }.xterm-dom-renderer-owner-1 .xterm-fg-24.xterm-dim { color: #005f8780; }.xterm-dom-renderer-owner-1 .xterm-bg-24 { background-color: #005f87; }.xterm-dom-renderer-owner-1 .xterm-fg-25 { color: #005faf; }.xterm-dom-renderer-owner-1 .xterm-fg-25.xterm-dim { color: #005faf80; }.xterm-dom-renderer-owner-1 .xterm-bg-25 { background-color: #005faf; }.xterm-dom-renderer-owner-1 .xterm-fg-26 { color: #005fd7; }.xterm-dom-renderer-owner-1 .xterm-fg-26.xterm-dim { color: #005fd780; }.xterm-dom-renderer-owner-1 .xterm-bg-26 { background-color: #005fd7; }.xterm-dom-renderer-owner-1 .xterm-fg-27 { color: #005fff; }.xterm-dom-renderer-owner-1 .xterm-fg-27.xterm-dim { color: #005fff80; }.xterm-dom-renderer-owner-1 .xterm-bg-27 { background-color: #005fff; }.xterm-dom-renderer-owner-1 .xterm-fg-28 { color: #008700; }.xterm-dom-renderer-owner-1 .xterm-fg-28.xterm-dim { color: #00870080; }.xterm-dom-renderer-owner-1 .xterm-bg-28 { background-color: #008700; }.xterm-dom-renderer-owner-1 .xterm-fg-29 { color: #00875f; }.xterm-dom-renderer-owner-1 .xterm-fg-29.xterm-dim { color: #00875f80; }.xterm-dom-renderer-owner-1 .xterm-bg-29 { background-color: #00875f; }.xterm-dom-renderer-owner-1 .xterm-fg-30 { color: #008787; }.xterm-dom-renderer-owner-1 .xterm-fg-30.xterm-dim { color: #00878780; }.xterm-dom-renderer-owner-1 .xterm-bg-30 { background-color: #008787; }.xterm-dom-renderer-owner-1 .xterm-fg-31 { color: #0087af; }.xterm-dom-renderer-owner-1 .xterm-fg-31.xterm-dim { color: #0087af80; }.xterm-dom-renderer-owner-1 .xterm-bg-31 { background-color: #0087af; }.xterm-dom-renderer-owner-1 .xterm-fg-32 { color: #0087d7; }.xterm-dom-renderer-owner-1 .xterm-fg-32.xterm-dim { color: #0087d780; }.xterm-dom-renderer-owner-1 .xterm-bg-32 { background-color: #0087d7; }.xterm-dom-renderer-owner-1 .xterm-fg-33 { color: #0087ff; }.xterm-dom-renderer-owner-1 .xterm-fg-33.xterm-dim { color: #0087ff80; }.xterm-dom-renderer-owner-1 .xterm-bg-33 { background-color: #0087ff; }.xterm-dom-renderer-owner-1 .xterm-fg-34 { color: #00af00; }.xterm-dom-renderer-owner-1 .xterm-fg-34.xterm-dim { color: #00af0080; }.xterm-dom-renderer-owner-1 .xterm-bg-34 { background-color: #00af00; }.xterm-dom-renderer-owner-1 .xterm-fg-35 { color: #00af5f; }.xterm-dom-renderer-owner-1 .xterm-fg-35.xterm-dim { color: #00af5f80; }.xterm-dom-renderer-owner-1 .xterm-bg-35 { background-color: #00af5f; }.xterm-dom-renderer-owner-1 .xterm-fg-36 { color: #00af87; }.xterm-dom-renderer-owner-1 .xterm-fg-36.xterm-dim { color: #00af8780; }.xterm-dom-renderer-owner-1 .xterm-bg-36 { background-color: #00af87; }.xterm-dom-renderer-owner-1 .xterm-fg-37 { color: #00afaf; }.xterm-dom-renderer-owner-1 .xterm-fg-37.xterm-dim { color: #00afaf80; }.xterm-dom-renderer-owner-1 .xterm-bg-37 { background-color: #00afaf; }.xterm-dom-renderer-owner-1 .xterm-fg-38 { color: #00afd7; }.xterm-dom-renderer-owner-1 .xterm-fg-38.xterm-dim { color: #00afd780; }.xterm-dom-renderer-owner-1 .xterm-bg-38 { background-color: #00afd7; }.xterm-dom-renderer-owner-1 .xterm-fg-39 { color: #00afff; }.xterm-dom-renderer-owner-1 .xterm-fg-39.xterm-dim { color: #00afff80; }.xterm-dom-renderer-owner-1 .xterm-bg-39 { background-color: #00afff; }.xterm-dom-renderer-owner-1 .xterm-fg-40 { color: #00d700; }.xterm-dom-renderer-owner-1 .xterm-fg-40.xterm-dim { color: #00d70080; }.xterm-dom-renderer-owner-1 .xterm-bg-40 { background-color: #00d700; }.xterm-dom-renderer-owner-1 .xterm-fg-41 { color: #00d75f; }.xterm-dom-renderer-owner-1 .xterm-fg-41.xterm-dim { color: #00d75f80; }.xterm-dom-renderer-owner-1 .xterm-bg-41 { background-color: #00d75f; }.xterm-dom-renderer-owner-1 .xterm-fg-42 { color: #00d787; }.xterm-dom-renderer-owner-1 .xterm-fg-42.xterm-dim { color: #00d78780; }.xterm-dom-renderer-owner-1 .xterm-bg-42 { background-color: #00d787; }.xterm-dom-renderer-owner-1 .xterm-fg-43 { color: #00d7af; }.xterm-dom-renderer-owner-1 .xterm-fg-43.xterm-dim { color: #00d7af80; }.xterm-dom-renderer-owner-1 .xterm-bg-43 { background-color: #00d7af; }.xterm-dom-renderer-owner-1 .xterm-fg-44 { color: #00d7d7; }.xterm-dom-renderer-owner-1 .xterm-fg-44.xterm-dim { color: #00d7d780; }.xterm-dom-renderer-owner-1 .xterm-bg-44 { background-color: #00d7d7; }.xterm-dom-renderer-owner-1 .xterm-fg-45 { color: #00d7ff; }.xterm-dom-renderer-owner-1 .xterm-fg-45.xterm-dim { color: #00d7ff80; }.xterm-dom-renderer-owner-1 .xterm-bg-45 { background-color: #00d7ff; }.xterm-dom-renderer-owner-1 .xterm-fg-46 { color: #00ff00; }.xterm-dom-renderer-owner-1 .xterm-fg-46.xterm-dim { color: #00ff0080; }.xterm-dom-renderer-owner-1 .xterm-bg-46 { background-color: #00ff00; }.xterm-dom-renderer-owner-1 .xterm-fg-47 { color: #00ff5f; }.xterm-dom-renderer-owner-1 .xterm-fg-47.xterm-dim { color: #00ff5f80; }.xterm-dom-renderer-owner-1 .xterm-bg-47 { background-color: #00ff5f; }.xterm-dom-renderer-owner-1 .xterm-fg-48 { color: #00ff87; }.xterm-dom-renderer-owner-1 .xterm-fg-48.xterm-dim { color: #00ff8780; }.xterm-dom-renderer-owner-1 .xterm-bg-48 { background-color: #00ff87; }.xterm-dom-renderer-owner-1 .xterm-fg-49 { color: #00ffaf; }.xterm-dom-renderer-owner-1 .xterm-fg-49.xterm-dim { color: #00ffaf80; }.xterm-dom-renderer-owner-1 .xterm-bg-49 { background-color: #00ffaf; }.xterm-dom-renderer-owner-1 .xterm-fg-50 { color: #00ffd7; }.xterm-dom-renderer-owner-1 .xterm-fg-50.xterm-dim { color: #00ffd780; }.xterm-dom-renderer-owner-1 .xterm-bg-50 { background-color: #00ffd7; }.xterm-dom-renderer-owner-1 .xterm-fg-51 { color: #00ffff; }.xterm-dom-renderer-owner-1 .xterm-fg-51.xterm-dim { color: #00ffff80; }.xterm-dom-renderer-owner-1 .xterm-bg-51 { background-color: #00ffff; }.xterm-dom-renderer-owner-1 .xterm-fg-52 { color: #5f0000; }.xterm-dom-renderer-owner-1 .xterm-fg-52.xterm-dim { color: #5f000080; }.xterm-dom-renderer-owner-1 .xterm-bg-52 { background-color: #5f0000; }.xterm-dom-renderer-owner-1 .xterm-fg-53 { color: #5f005f; }.xterm-dom-renderer-owner-1 .xterm-fg-53.xterm-dim { color: #5f005f80; }.xterm-dom-renderer-owner-1 .xterm-bg-53 { background-color: #5f005f; }.xterm-dom-renderer-owner-1 .xterm-fg-54 { color: #5f0087; }.xterm-dom-renderer-owner-1 .xterm-fg-54.xterm-dim { color: #5f008780; }.xterm-dom-renderer-owner-1 .xterm-bg-54 { background-color: #5f0087; }.xterm-dom-renderer-owner-1 .xterm-fg-55 { color: #5f00af; }.xterm-dom-renderer-owner-1 .xterm-fg-55.xterm-dim { color: #5f00af80; }.xterm-dom-renderer-owner-1 .xterm-bg-55 { background-color: #5f00af; }.xterm-dom-renderer-owner-1 .xterm-fg-56 { color: #5f00d7; }.xterm-dom-renderer-owner-1 .xterm-fg-56.xterm-dim { color: #5f00d780; }.xterm-dom-renderer-owner-1 .xterm-bg-56 { background-color: #5f00d7; }.xterm-dom-renderer-owner-1 .xterm-fg-57 { color: #5f00ff; }.xterm-dom-renderer-owner-1 .xterm-fg-57.xterm-dim { color: #5f00ff80; }.xterm-dom-renderer-owner-1 .xterm-bg-57 { background-color: #5f00ff; }.xterm-dom-renderer-owner-1 .xterm-fg-58 { color: #5f5f00; }.xterm-dom-renderer-owner-1 .xterm-fg-58.xterm-dim { color: #5f5f0080; }.xterm-dom-renderer-owner-1 .xterm-bg-58 { background-color: #5f5f00; }.xterm-dom-renderer-owner-1 .xterm-fg-59 { color: #5f5f5f; }.xterm-dom-renderer-owner-1 .xterm-fg-59.xterm-dim { color: #5f5f5f80; }.xterm-dom-renderer-owner-1 .xterm-bg-59 { background-color: #5f5f5f; }.xterm-dom-renderer-owner-1 .xterm-fg-60 { color: #5f5f87; }.xterm-dom-renderer-owner-1 .xterm-fg-60.xterm-dim { color: #5f5f8780; }.xterm-dom-renderer-owner-1 .xterm-bg-60 { background-color: #5f5f87; }.xterm-dom-renderer-owner-1 .xterm-fg-61 { color: #5f5faf; }.xterm-dom-renderer-owner-1 .xterm-fg-61.xterm-dim { color: #5f5faf80; }.xterm-dom-renderer-owner-1 .xterm-bg-61 { background-color: #5f5faf; }.xterm-dom-renderer-owner-1 .xterm-fg-62 { color: #5f5fd7; }.xterm-dom-renderer-owner-1 .xterm-fg-62.xterm-dim { color: #5f5fd780; }.xterm-dom-renderer-owner-1 .xterm-bg-62 { background-color: #5f5fd7; }.xterm-dom-renderer-owner-1 .xterm-fg-63 { color: #5f5fff; }.xterm-dom-renderer-owner-1 .xterm-fg-63.xterm-dim { color: #5f5fff80; }.xterm-dom-renderer-owner-1 .xterm-bg-63 { background-color: #5f5fff; }.xterm-dom-renderer-owner-1 .xterm-fg-64 { color: #5f8700; }.xterm-dom-renderer-owner-1 .xterm-fg-64.xterm-dim { color: #5f870080; }.xterm-dom-renderer-owner-1 .xterm-bg-64 { background-color: #5f8700; }.xterm-dom-renderer-owner-1 .xterm-fg-65 { color: #5f875f; }.xterm-dom-renderer-owner-1 .xterm-fg-65.xterm-dim { color: #5f875f80; }.xterm-dom-renderer-owner-1 .xterm-bg-65 { background-color: #5f875f; }.xterm-dom-renderer-owner-1 .xterm-fg-66 { color: #5f8787; }.xterm-dom-renderer-owner-1 .xterm-fg-66.xterm-dim { color: #5f878780; }.xterm-dom-renderer-owner-1 .xterm-bg-66 { background-color: #5f8787; }.xterm-dom-renderer-owner-1 .xterm-fg-67 { color: #5f87af; }.xterm-dom-renderer-owner-1 .xterm-fg-67.xterm-dim { color: #5f87af80; }.xterm-dom-renderer-owner-1 .xterm-bg-67 { background-color: #5f87af; }.xterm-dom-renderer-owner-1 .xterm-fg-68 { color: #5f87d7; }.xterm-dom-renderer-owner-1 .xterm-fg-68.xterm-dim { color: #5f87d780; }.xterm-dom-renderer-owner-1 .xterm-bg-68 { background-color: #5f87d7; }.xterm-dom-renderer-owner-1 .xterm-fg-69 { color: #5f87ff; }.xterm-dom-renderer-owner-1 .xterm-fg-69.xterm-dim { color: #5f87ff80; }.xterm-dom-renderer-owner-1 .xterm-bg-69 { background-color: #5f87ff; }.xterm-dom-renderer-owner-1 .xterm-fg-70 { color: #5faf00; }.xterm-dom-renderer-owner-1 .xterm-fg-70.xterm-dim { color: #5faf0080; }.xterm-dom-renderer-owner-1 .xterm-bg-70 { background-color: #5faf00; }.xterm-dom-renderer-owner-1 .xterm-fg-71 { color: #5faf5f; }.xterm-dom-renderer-owner-1 .xterm-fg-71.xterm-dim { color: #5faf5f80; }.xterm-dom-renderer-owner-1 .xterm-bg-71 { background-color: #5faf5f; }.xterm-dom-renderer-owner-1 .xterm-fg-72 { color: #5faf87; }.xterm-dom-renderer-owner-1 .xterm-fg-72.xterm-dim { color: #5faf8780; }.xterm-dom-renderer-owner-1 .xterm-bg-72 { background-color: #5faf87; }.xterm-dom-renderer-owner-1 .xterm-fg-73 { color: #5fafaf; }.xterm-dom-renderer-owner-1 .xterm-fg-73.xterm-dim { color: #5fafaf80; }.xterm-dom-renderer-owner-1 .xterm-bg-73 { background-color: #5fafaf; }.xterm-dom-renderer-owner-1 .xterm-fg-74 { color: #5fafd7; }.xterm-dom-renderer-owner-1 .xterm-fg-74.xterm-dim { color: #5fafd780; }.xterm-dom-renderer-owner-1 .xterm-bg-74 { background-color: #5fafd7; }.xterm-dom-renderer-owner-1 .xterm-fg-75 { color: #5fafff; }.xterm-dom-renderer-owner-1 .xterm-fg-75.xterm-dim { color: #5fafff80; }.xterm-dom-renderer-owner-1 .xterm-bg-75 { background-color: #5fafff; }.xterm-dom-renderer-owner-1 .xterm-fg-76 { color: #5fd700; }.xterm-dom-renderer-owner-1 .xterm-fg-76.xterm-dim { color: #5fd70080; }.xterm-dom-renderer-owner-1 .xterm-bg-76 { background-color: #5fd700; }.xterm-dom-renderer-owner-1 .xterm-fg-77 { color: #5fd75f; }.xterm-dom-renderer-owner-1 .xterm-fg-77.xterm-dim { color: #5fd75f80; }.xterm-dom-renderer-owner-1 .xterm-bg-77 { background-color: #5fd75f; }.xterm-dom-renderer-owner-1 .xterm-fg-78 { color: #5fd787; }.xterm-dom-renderer-owner-1 .xterm-fg-78.xterm-dim { color: #5fd78780; }.xterm-dom-renderer-owner-1 .xterm-bg-78 { background-color: #5fd787; }.xterm-dom-renderer-owner-1 .xterm-fg-79 { color: #5fd7af; }.xterm-dom-renderer-owner-1 .xterm-fg-79.xterm-dim { color: #5fd7af80; }.xterm-dom-renderer-owner-1 .xterm-bg-79 { background-color: #5fd7af; }.xterm-dom-renderer-owner-1 .xterm-fg-80 { color: #5fd7d7; }.xterm-dom-renderer-owner-1 .xterm-fg-80.xterm-dim { color: #5fd7d780; }.xterm-dom-renderer-owner-1 .xterm-bg-80 { background-color: #5fd7d7; }.xterm-dom-renderer-owner-1 .xterm-fg-81 { color: #5fd7ff; }.xterm-dom-renderer-owner-1 .xterm-fg-81.xterm-dim { color: #5fd7ff80; }.xterm-dom-renderer-owner-1 .xterm-bg-81 { background-color: #5fd7ff; }.xterm-dom-renderer-owner-1 .xterm-fg-82 { color: #5fff00; }.xterm-dom-renderer-owner-1 .xterm-fg-82.xterm-dim { color: #5fff0080; }.xterm-dom-renderer-owner-1 .xterm-bg-82 { background-color: #5fff00; }.xterm-dom-renderer-owner-1 .xterm-fg-83 { color: #5fff5f; }.xterm-dom-renderer-owner-1 .xterm-fg-83.xterm-dim { color: #5fff5f80; }.xterm-dom-renderer-owner-1 .xterm-bg-83 { background-color: #5fff5f; }.xterm-dom-renderer-owner-1 .xterm-fg-84 { color: #5fff87; }.xterm-dom-renderer-owner-1 .xterm-fg-84.xterm-dim { color: #5fff8780; }.xterm-dom-renderer-owner-1 .xterm-bg-84 { background-color: #5fff87; }.xterm-dom-renderer-owner-1 .xterm-fg-85 { color: #5fffaf; }.xterm-dom-renderer-owner-1 .xterm-fg-85.xterm-dim { color: #5fffaf80; }.xterm-dom-renderer-owner-1 .xterm-bg-85 { background-color: #5fffaf; }.xterm-dom-renderer-owner-1 .xterm-fg-86 { color: #5fffd7; }.xterm-dom-renderer-owner-1 .xterm-fg-86.xterm-dim { color: #5fffd780; }.xterm-dom-renderer-owner-1 .xterm-bg-86 { background-color: #5fffd7; }.xterm-dom-renderer-owner-1 .xterm-fg-87 { color: #5fffff; }.xterm-dom-renderer-owner-1 .xterm-fg-87.xterm-dim { color: #5fffff80; }.xterm-dom-renderer-owner-1 .xterm-bg-87 { background-color: #5fffff; }.xterm-dom-renderer-owner-1 .xterm-fg-88 { color: #870000; }.xterm-dom-renderer-owner-1 .xterm-fg-88.xterm-dim { color: #87000080; }.xterm-dom-renderer-owner-1 .xterm-bg-88 { background-color: #870000; }.xterm-dom-renderer-owner-1 .xterm-fg-89 { color: #87005f; }.xterm-dom-renderer-owner-1 .xterm-fg-89.xterm-dim { color: #87005f80; }.xterm-dom-renderer-owner-1 .xterm-bg-89 { background-color: #87005f; }.xterm-dom-renderer-owner-1 .xterm-fg-90 { color: #870087; }.xterm-dom-renderer-owner-1 .xterm-fg-90.xterm-dim { color: #87008780; }.xterm-dom-renderer-owner-1 .xterm-bg-90 { background-color: #870087; }.xterm-dom-renderer-owner-1 .xterm-fg-91 { color: #8700af; }.xterm-dom-renderer-owner-1 .xterm-fg-91.xterm-dim { color: #8700af80; }.xterm-dom-renderer-owner-1 .xterm-bg-91 { background-color: #8700af; }.xterm-dom-renderer-owner-1 .xterm-fg-92 { color: #8700d7; }.xterm-dom-renderer-owner-1 .xterm-fg-92.xterm-dim { color: #8700d780; }.xterm-dom-renderer-owner-1 .xterm-bg-92 { background-color: #8700d7; }.xterm-dom-renderer-owner-1 .xterm-fg-93 { color: #8700ff; }.xterm-dom-renderer-owner-1 .xterm-fg-93.xterm-dim { color: #8700ff80; }.xterm-dom-renderer-owner-1 .xterm-bg-93 { background-color: #8700ff; }.xterm-dom-renderer-owner-1 .xterm-fg-94 { color: #875f00; }.xterm-dom-renderer-owner-1 .xterm-fg-94.xterm-dim { color: #875f0080; }.xterm-dom-renderer-owner-1 .xterm-bg-94 { background-color: #875f00; }.xterm-dom-renderer-owner-1 .xterm-fg-95 { color: #875f5f; }.xterm-dom-renderer-owner-1 .xterm-fg-95.xterm-dim { color: #875f5f80; }.xterm-dom-renderer-owner-1 .xterm-bg-95 { background-color: #875f5f; }.xterm-dom-renderer-owner-1 .xterm-fg-96 { color: #875f87; }.xterm-dom-renderer-owner-1 .xterm-fg-96.xterm-dim { color: #875f8780; }.xterm-dom-renderer-owner-1 .xterm-bg-96 { background-color: #875f87; }.xterm-dom-renderer-owner-1 .xterm-fg-97 { color: #875faf; }.xterm-dom-renderer-owner-1 .xterm-fg-97.xterm-dim { color: #875faf80; }.xterm-dom-renderer-owner-1 .xterm-bg-97 { background-color: #875faf; }.xterm-dom-renderer-owner-1 .xterm-fg-98 { color: #875fd7; }.xterm-dom-renderer-owner-1 .xterm-fg-98.xterm-dim { color: #875fd780; }.xterm-dom-renderer-owner-1 .xterm-bg-98 { background-color: #875fd7; }.xterm-dom-renderer-owner-1 .xterm-fg-99 { color: #875fff; }.xterm-dom-renderer-owner-1 .xterm-fg-99.xterm-dim { color: #875fff80; }.xterm-dom-renderer-owner-1 .xterm-bg-99 { background-color: #875fff; }.xterm-dom-renderer-owner-1 .xterm-fg-100 { color: #878700; }.xterm-dom-renderer-owner-1 .xterm-fg-100.xterm-dim { color: #87870080; }.xterm-dom-renderer-owner-1 .xterm-bg-100 { background-color: #878700; }.xterm-dom-renderer-owner-1 .xterm-fg-101 { color: #87875f; }.xterm-dom-renderer-owner-1 .xterm-fg-101.xterm-dim { color: #87875f80; }.xterm-dom-renderer-owner-1 .xterm-bg-101 { background-color: #87875f; }.xterm-dom-renderer-owner-1 .xterm-fg-102 { color: #878787; }.xterm-dom-renderer-owner-1 .xterm-fg-102.xterm-dim { color: #87878780; }.xterm-dom-renderer-owner-1 .xterm-bg-102 { background-color: #878787; }.xterm-dom-renderer-owner-1 .xterm-fg-103 { color: #8787af; }.xterm-dom-renderer-owner-1 .xterm-fg-103.xterm-dim { color: #8787af80; }.xterm-dom-renderer-owner-1 .xterm-bg-103 { background-color: #8787af; }.xterm-dom-renderer-owner-1 .xterm-fg-104 { color: #8787d7; }.xterm-dom-renderer-owner-1 .xterm-fg-104.xterm-dim { color: #8787d780; }.xterm-dom-renderer-owner-1 .xterm-bg-104 { background-color: #8787d7; }.xterm-dom-renderer-owner-1 .xterm-fg-105 { color: #8787ff; }.xterm-dom-renderer-owner-1 .xterm-fg-105.xterm-dim { color: #8787ff80; }.xterm-dom-renderer-owner-1 .xterm-bg-105 { background-color: #8787ff; }.xterm-dom-renderer-owner-1 .xterm-fg-106 { color: #87af00; }.xterm-dom-renderer-owner-1 .xterm-fg-106.xterm-dim { color: #87af0080; }.xterm-dom-renderer-owner-1 .xterm-bg-106 { background-color: #87af00; }.xterm-dom-renderer-owner-1 .xterm-fg-107 { color: #87af5f; }.xterm-dom-renderer-owner-1 .xterm-fg-107.xterm-dim { color: #87af5f80; }.xterm-dom-renderer-owner-1 .xterm-bg-107 { background-color: #87af5f; }.xterm-dom-renderer-owner-1 .xterm-fg-108 { color: #87af87; }.xterm-dom-renderer-owner-1 .xterm-fg-108.xterm-dim { color: #87af8780; }.xterm-dom-renderer-owner-1 .xterm-bg-108 { background-color: #87af87; }.xterm-dom-renderer-owner-1 .xterm-fg-109 { color: #87afaf; }.xterm-dom-renderer-owner-1 .xterm-fg-109.xterm-dim { color: #87afaf80; }.xterm-dom-renderer-owner-1 .xterm-bg-109 { background-color: #87afaf; }.xterm-dom-renderer-owner-1 .xterm-fg-110 { color: #87afd7; }.xterm-dom-renderer-owner-1 .xterm-fg-110.xterm-dim { color: #87afd780; }.xterm-dom-renderer-owner-1 .xterm-bg-110 { background-color: #87afd7; }.xterm-dom-renderer-owner-1 .xterm-fg-111 { color: #87afff; }.xterm-dom-renderer-owner-1 .xterm-fg-111.xterm-dim { color: #87afff80; }.xterm-dom-renderer-owner-1 .xterm-bg-111 { background-color: #87afff; }.xterm-dom-renderer-owner-1 .xterm-fg-112 { color: #87d700; }.xterm-dom-renderer-owner-1 .xterm-fg-112.xterm-dim { color: #87d70080; }.xterm-dom-renderer-owner-1 .xterm-bg-112 { background-color: #87d700; }.xterm-dom-renderer-owner-1 .xterm-fg-113 { color: #87d75f; }.xterm-dom-renderer-owner-1 .xterm-fg-113.xterm-dim { color: #87d75f80; }.xterm-dom-renderer-owner-1 .xterm-bg-113 { background-color: #87d75f; }.xterm-dom-renderer-owner-1 .xterm-fg-114 { color: #87d787; }.xterm-dom-renderer-owner-1 .xterm-fg-114.xterm-dim { color: #87d78780; }.xterm-dom-renderer-owner-1 .xterm-bg-114 { background-color: #87d787; }.xterm-dom-renderer-owner-1 .xterm-fg-115 { color: #87d7af; }.xterm-dom-renderer-owner-1 .xterm-fg-115.xterm-dim { color: #87d7af80; }.xterm-dom-renderer-owner-1 .xterm-bg-115 { background-color: #87d7af; }.xterm-dom-renderer-owner-1 .xterm-fg-116 { color: #87d7d7; }.xterm-dom-renderer-owner-1 .xterm-fg-116.xterm-dim { color: #87d7d780; }.xterm-dom-renderer-owner-1 .xterm-bg-116 { background-color: #87d7d7; }.xterm-dom-renderer-owner-1 .xterm-fg-117 { color: #87d7ff; }.xterm-dom-renderer-owner-1 .xterm-fg-117.xterm-dim { color: #87d7ff80; }.xterm-dom-renderer-owner-1 .xterm-bg-117 { background-color: #87d7ff; }.xterm-dom-renderer-owner-1 .xterm-fg-118 { color: #87ff00; }.xterm-dom-renderer-owner-1 .xterm-fg-118.xterm-dim { color: #87ff0080; }.xterm-dom-renderer-owner-1 .xterm-bg-118 { background-color: #87ff00; }.xterm-dom-renderer-owner-1 .xterm-fg-119 { color: #87ff5f; }.xterm-dom-renderer-owner-1 .xterm-fg-119.xterm-dim { color: #87ff5f80; }.xterm-dom-renderer-owner-1 .xterm-bg-119 { background-color: #87ff5f; }.xterm-dom-renderer-owner-1 .xterm-fg-120 { color: #87ff87; }.xterm-dom-renderer-owner-1 .xterm-fg-120.xterm-dim { color: #87ff8780; }.xterm-dom-renderer-owner-1 .xterm-bg-120 { background-color: #87ff87; }.xterm-dom-renderer-owner-1 .xterm-fg-121 { color: #87ffaf; }.xterm-dom-renderer-owner-1 .xterm-fg-121.xterm-dim { color: #87ffaf80; }.xterm-dom-renderer-owner-1 .xterm-bg-121 { background-color: #87ffaf; }.xterm-dom-renderer-owner-1 .xterm-fg-122 { color: #87ffd7; }.xterm-dom-renderer-owner-1 .xterm-fg-122.xterm-dim { color: #87ffd780; }.xterm-dom-renderer-owner-1 .xterm-bg-122 { background-color: #87ffd7; }.xterm-dom-renderer-owner-1 .xterm-fg-123 { color: #87ffff; }.xterm-dom-renderer-owner-1 .xterm-fg-123.xterm-dim { color: #87ffff80; }.xterm-dom-renderer-owner-1 .xterm-bg-123 { background-color: #87ffff; }.xterm-dom-renderer-owner-1 .xterm-fg-124 { color: #af0000; }.xterm-dom-renderer-owner-1 .xterm-fg-124.xterm-dim { color: #af000080; }.xterm-dom-renderer-owner-1 .xterm-bg-124 { background-color: #af0000; }.xterm-dom-renderer-owner-1 .xterm-fg-125 { color: #af005f; }.xterm-dom-renderer-owner-1 .xterm-fg-125.xterm-dim { color: #af005f80; }.xterm-dom-renderer-owner-1 .xterm-bg-125 { background-color: #af005f; }.xterm-dom-renderer-owner-1 .xterm-fg-126 { color: #af0087; }.xterm-dom-renderer-owner-1 .xterm-fg-126.xterm-dim { color: #af008780; }.xterm-dom-renderer-owner-1 .xterm-bg-126 { background-color: #af0087; }.xterm-dom-renderer-owner-1 .xterm-fg-127 { color: #af00af; }.xterm-dom-renderer-owner-1 .xterm-fg-127.xterm-dim { color: #af00af80; }.xterm-dom-renderer-owner-1 .xterm-bg-127 { background-color: #af00af; }.xterm-dom-renderer-owner-1 .xterm-fg-128 { color: #af00d7; }.xterm-dom-renderer-owner-1 .xterm-fg-128.xterm-dim { color: #af00d780; }.xterm-dom-renderer-owner-1 .xterm-bg-128 { background-color: #af00d7; }.xterm-dom-renderer-owner-1 .xterm-fg-129 { color: #af00ff; }.xterm-dom-renderer-owner-1 .xterm-fg-129.xterm-dim { color: #af00ff80; }.xterm-dom-renderer-owner-1 .xterm-bg-129 { background-color: #af00ff; }.xterm-dom-renderer-owner-1 .xterm-fg-130 { color: #af5f00; }.xterm-dom-renderer-owner-1 .xterm-fg-130.xterm-dim { color: #af5f0080; }.xterm-dom-renderer-owner-1 .xterm-bg-130 { background-color: #af5f00; }.xterm-dom-renderer-owner-1 .xterm-fg-131 { color: #af5f5f; }.xterm-dom-renderer-owner-1 .xterm-fg-131.xterm-dim { color: #af5f5f80; }.xterm-dom-renderer-owner-1 .xterm-bg-131 { background-color: #af5f5f; }.xterm-dom-renderer-owner-1 .xterm-fg-132 { color: #af5f87; }.xterm-dom-renderer-owner-1 .xterm-fg-132.xterm-dim { color: #af5f8780; }.xterm-dom-renderer-owner-1 .xterm-bg-132 { background-color: #af5f87; }.xterm-dom-renderer-owner-1 .xterm-fg-133 { color: #af5faf; }.xterm-dom-renderer-owner-1 .xterm-fg-133.xterm-dim { color: #af5faf80; }.xterm-dom-renderer-owner-1 .xterm-bg-133 { background-color: #af5faf; }.xterm-dom-renderer-owner-1 .xterm-fg-134 { color: #af5fd7; }.xterm-dom-renderer-owner-1 .xterm-fg-134.xterm-dim { color: #af5fd780; }.xterm-dom-renderer-owner-1 .xterm-bg-134 { background-color: #af5fd7; }.xterm-dom-renderer-owner-1 .xterm-fg-135 { color: #af5fff; }.xterm-dom-renderer-owner-1 .xterm-fg-135.xterm-dim { color: #af5fff80; }.xterm-dom-renderer-owner-1 .xterm-bg-135 { background-color: #af5fff; }.xterm-dom-renderer-owner-1 .xterm-fg-136 { color: #af8700; }.xterm-dom-renderer-owner-1 .xterm-fg-136.xterm-dim { color: #af870080; }.xterm-dom-renderer-owner-1 .xterm-bg-136 { background-color: #af8700; }.xterm-dom-renderer-owner-1 .xterm-fg-137 { color: #af875f; }.xterm-dom-renderer-owner-1 .xterm-fg-137.xterm-dim { color: #af875f80; }.xterm-dom-renderer-owner-1 .xterm-bg-137 { background-color: #af875f; }.xterm-dom-renderer-owner-1 .xterm-fg-138 { color: #af8787; }.xterm-dom-renderer-owner-1 .xterm-fg-138.xterm-dim { color: #af878780; }.xterm-dom-renderer-owner-1 .xterm-bg-138 { background-color: #af8787; }.xterm-dom-renderer-owner-1 .xterm-fg-139 { color: #af87af; }.xterm-dom-renderer-owner-1 .xterm-fg-139.xterm-dim { color: #af87af80; }.xterm-dom-renderer-owner-1 .xterm-bg-139 { background-color: #af87af; }.xterm-dom-renderer-owner-1 .xterm-fg-140 { color: #af87d7; }.xterm-dom-renderer-owner-1 .xterm-fg-140.xterm-dim { color: #af87d780; }.xterm-dom-renderer-owner-1 .xterm-bg-140 { background-color: #af87d7; }.xterm-dom-renderer-owner-1 .xterm-fg-141 { color: #af87ff; }.xterm-dom-renderer-owner-1 .xterm-fg-141.xterm-dim { color: #af87ff80; }.xterm-dom-renderer-owner-1 .xterm-bg-141 { background-color: #af87ff; }.xterm-dom-renderer-owner-1 .xterm-fg-142 { color: #afaf00; }.xterm-dom-renderer-owner-1 .xterm-fg-142.xterm-dim { color: #afaf0080; }.xterm-dom-renderer-owner-1 .xterm-bg-142 { background-color: #afaf00; }.xterm-dom-renderer-owner-1 .xterm-fg-143 { color: #afaf5f; }.xterm-dom-renderer-owner-1 .xterm-fg-143.xterm-dim { color: #afaf5f80; }.xterm-dom-renderer-owner-1 .xterm-bg-143 { background-color: #afaf5f; }.xterm-dom-renderer-owner-1 .xterm-fg-144 { color: #afaf87; }.xterm-dom-renderer-owner-1 .xterm-fg-144.xterm-dim { color: #afaf8780; }.xterm-dom-renderer-owner-1 .xterm-bg-144 { background-color: #afaf87; }.xterm-dom-renderer-owner-1 .xterm-fg-145 { color: #afafaf; }.xterm-dom-renderer-owner-1 .xterm-fg-145.xterm-dim { color: #afafaf80; }.xterm-dom-renderer-owner-1 .xterm-bg-145 { background-color: #afafaf; }.xterm-dom-renderer-owner-1 .xterm-fg-146 { color: #afafd7; }.xterm-dom-renderer-owner-1 .xterm-fg-146.xterm-dim { color: #afafd780; }.xterm-dom-renderer-owner-1 .xterm-bg-146 { background-color: #afafd7; }.xterm-dom-renderer-owner-1 .xterm-fg-147 { color: #afafff; }.xterm-dom-renderer-owner-1 .xterm-fg-147.xterm-dim { color: #afafff80; }.xterm-dom-renderer-owner-1 .xterm-bg-147 { background-color: #afafff; }.xterm-dom-renderer-owner-1 .xterm-fg-148 { color: #afd700; }.xterm-dom-renderer-owner-1 .xterm-fg-148.xterm-dim { color: #afd70080; }.xterm-dom-renderer-owner-1 .xterm-bg-148 { background-color: #afd700; }.xterm-dom-renderer-owner-1 .xterm-fg-149 { color: #afd75f; }.xterm-dom-renderer-owner-1 .xterm-fg-149.xterm-dim { color: #afd75f80; }.xterm-dom-renderer-owner-1 .xterm-bg-149 { background-color: #afd75f; }.xterm-dom-renderer-owner-1 .xterm-fg-150 { color: #afd787; }.xterm-dom-renderer-owner-1 .xterm-fg-150.xterm-dim { color: #afd78780; }.xterm-dom-renderer-owner-1 .xterm-bg-150 { background-color: #afd787; }.xterm-dom-renderer-owner-1 .xterm-fg-151 { color: #afd7af; }.xterm-dom-renderer-owner-1 .xterm-fg-151.xterm-dim { color: #afd7af80; }.xterm-dom-renderer-owner-1 .xterm-bg-151 { background-color: #afd7af; }.xterm-dom-renderer-owner-1 .xterm-fg-152 { color: #afd7d7; }.xterm-dom-renderer-owner-1 .xterm-fg-152.xterm-dim { color: #afd7d780; }.xterm-dom-renderer-owner-1 .xterm-bg-152 { background-color: #afd7d7; }.xterm-dom-renderer-owner-1 .xterm-fg-153 { color: #afd7ff; }.xterm-dom-renderer-owner-1 .xterm-fg-153.xterm-dim { color: #afd7ff80; }.xterm-dom-renderer-owner-1 .xterm-bg-153 { background-color: #afd7ff; }.xterm-dom-renderer-owner-1 .xterm-fg-154 { color: #afff00; }.xterm-dom-renderer-owner-1 .xterm-fg-154.xterm-dim { color: #afff0080; }.xterm-dom-renderer-owner-1 .xterm-bg-154 { background-color: #afff00; }.xterm-dom-renderer-owner-1 .xterm-fg-155 { color: #afff5f; }.xterm-dom-renderer-owner-1 .xterm-fg-155.xterm-dim { color: #afff5f80; }.xterm-dom-renderer-owner-1 .xterm-bg-155 { background-color: #afff5f; }.xterm-dom-renderer-owner-1 .xterm-fg-156 { color: #afff87; }.xterm-dom-renderer-owner-1 .xterm-fg-156.xterm-dim { color: #afff8780; }.xterm-dom-renderer-owner-1 .xterm-bg-156 { background-color: #afff87; }.xterm-dom-renderer-owner-1 .xterm-fg-157 { color: #afffaf; }.xterm-dom-renderer-owner-1 .xterm-fg-157.xterm-dim { color: #afffaf80; }.xterm-dom-renderer-owner-1 .xterm-bg-157 { background-color: #afffaf; }.xterm-dom-renderer-owner-1 .xterm-fg-158 { color: #afffd7; }.xterm-dom-renderer-owner-1 .xterm-fg-158.xterm-dim { color: #afffd780; }.xterm-dom-renderer-owner-1 .xterm-bg-158 { background-color: #afffd7; }.xterm-dom-renderer-owner-1 .xterm-fg-159 { color: #afffff; }.xterm-dom-renderer-owner-1 .xterm-fg-159.xterm-dim { color: #afffff80; }.xterm-dom-renderer-owner-1 .xterm-bg-159 { background-color: #afffff; }.xterm-dom-renderer-owner-1 .xterm-fg-160 { color: #d70000; }.xterm-dom-renderer-owner-1 .xterm-fg-160.xterm-dim { color: #d7000080; }.xterm-dom-renderer-owner-1 .xterm-bg-160 { background-color: #d70000; }.xterm-dom-renderer-owner-1 .xterm-fg-161 { color: #d7005f; }.xterm-dom-renderer-owner-1 .xterm-fg-161.xterm-dim { color: #d7005f80; }.xterm-dom-renderer-owner-1 .xterm-bg-161 { background-color: #d7005f; }.xterm-dom-renderer-owner-1 .xterm-fg-162 { color: #d70087; }.xterm-dom-renderer-owner-1 .xterm-fg-162.xterm-dim { color: #d7008780; }.xterm-dom-renderer-owner-1 .xterm-bg-162 { background-color: #d70087; }.xterm-dom-renderer-owner-1 .xterm-fg-163 { color: #d700af; }.xterm-dom-renderer-owner-1 .xterm-fg-163.xterm-dim { color: #d700af80; }.xterm-dom-renderer-owner-1 .xterm-bg-163 { background-color: #d700af; }.xterm-dom-renderer-owner-1 .xterm-fg-164 { color: #d700d7; }.xterm-dom-renderer-owner-1 .xterm-fg-164.xterm-dim { color: #d700d780; }.xterm-dom-renderer-owner-1 .xterm-bg-164 { background-color: #d700d7; }.xterm-dom-renderer-owner-1 .xterm-fg-165 { color: #d700ff; }.xterm-dom-renderer-owner-1 .xterm-fg-165.xterm-dim { color: #d700ff80; }.xterm-dom-renderer-owner-1 .xterm-bg-165 { background-color: #d700ff; }.xterm-dom-renderer-owner-1 .xterm-fg-166 { color: #d75f00; }.xterm-dom-renderer-owner-1 .xterm-fg-166.xterm-dim { color: #d75f0080; }.xterm-dom-renderer-owner-1 .xterm-bg-166 { background-color: #d75f00; }.xterm-dom-renderer-owner-1 .xterm-fg-167 { color: #d75f5f; }.xterm-dom-renderer-owner-1 .xterm-fg-167.xterm-dim { color: #d75f5f80; }.xterm-dom-renderer-owner-1 .xterm-bg-167 { background-color: #d75f5f; }.xterm-dom-renderer-owner-1 .xterm-fg-168 { color: #d75f87; }.xterm-dom-renderer-owner-1 .xterm-fg-168.xterm-dim { color: #d75f8780; }.xterm-dom-renderer-owner-1 .xterm-bg-168 { background-color: #d75f87; }.xterm-dom-renderer-owner-1 .xterm-fg-169 { color: #d75faf; }.xterm-dom-renderer-owner-1 .xterm-fg-169.xterm-dim { color: #d75faf80; }.xterm-dom-renderer-owner-1 .xterm-bg-169 { background-color: #d75faf; }.xterm-dom-renderer-owner-1 .xterm-fg-170 { color: #d75fd7; }.xterm-dom-renderer-owner-1 .xterm-fg-170.xterm-dim { color: #d75fd780; }.xterm-dom-renderer-owner-1 .xterm-bg-170 { background-color: #d75fd7; }.xterm-dom-renderer-owner-1 .xterm-fg-171 { color: #d75fff; }.xterm-dom-renderer-owner-1 .xterm-fg-171.xterm-dim { color: #d75fff80; }.xterm-dom-renderer-owner-1 .xterm-bg-171 { background-color: #d75fff; }.xterm-dom-renderer-owner-1 .xterm-fg-172 { color: #d78700; }.xterm-dom-renderer-owner-1 .xterm-fg-172.xterm-dim { color: #d7870080; }.xterm-dom-renderer-owner-1 .xterm-bg-172 { background-color: #d78700; }.xterm-dom-renderer-owner-1 .xterm-fg-173 { color: #d7875f; }.xterm-dom-renderer-owner-1 .xterm-fg-173.xterm-dim { color: #d7875f80; }.xterm-dom-renderer-owner-1 .xterm-bg-173 { background-color: #d7875f; }.xterm-dom-renderer-owner-1 .xterm-fg-174 { color: #d78787; }.xterm-dom-renderer-owner-1 .xterm-fg-174.xterm-dim { color: #d7878780; }.xterm-dom-renderer-owner-1 .xterm-bg-174 { background-color: #d78787; }.xterm-dom-renderer-owner-1 .xterm-fg-175 { color: #d787af; }.xterm-dom-renderer-owner-1 .xterm-fg-175.xterm-dim { color: #d787af80; }.xterm-dom-renderer-owner-1 .xterm-bg-175 { background-color: #d787af; }.xterm-dom-renderer-owner-1 .xterm-fg-176 { color: #d787d7; }.xterm-dom-renderer-owner-1 .xterm-fg-176.xterm-dim { color: #d787d780; }.xterm-dom-renderer-owner-1 .xterm-bg-176 { background-color: #d787d7; }.xterm-dom-renderer-owner-1 .xterm-fg-177 { color: #d787ff; }.xterm-dom-renderer-owner-1 .xterm-fg-177.xterm-dim { color: #d787ff80; }.xterm-dom-renderer-owner-1 .xterm-bg-177 { background-color: #d787ff; }.xterm-dom-renderer-owner-1 .xterm-fg-178 { color: #d7af00; }.xterm-dom-renderer-owner-1 .xterm-fg-178.xterm-dim { color: #d7af0080; }.xterm-dom-renderer-owner-1 .xterm-bg-178 { background-color: #d7af00; }.xterm-dom-renderer-owner-1 .xterm-fg-179 { color: #d7af5f; }.xterm-dom-renderer-owner-1 .xterm-fg-179.xterm-dim { color: #d7af5f80; }.xterm-dom-renderer-owner-1 .xterm-bg-179 { background-color: #d7af5f; }.xterm-dom-renderer-owner-1 .xterm-fg-180 { color: #d7af87; }.xterm-dom-renderer-owner-1 .xterm-fg-180.xterm-dim { color: #d7af8780; }.xterm-dom-renderer-owner-1 .xterm-bg-180 { background-color: #d7af87; }.xterm-dom-renderer-owner-1 .xterm-fg-181 { color: #d7afaf; }.xterm-dom-renderer-owner-1 .xterm-fg-181.xterm-dim { color: #d7afaf80; }.xterm-dom-renderer-owner-1 .xterm-bg-181 { background-color: #d7afaf; }.xterm-dom-renderer-owner-1 .xterm-fg-182 { color: #d7afd7; }.xterm-dom-renderer-owner-1 .xterm-fg-182.xterm-dim { color: #d7afd780; }.xterm-dom-renderer-owner-1 .xterm-bg-182 { background-color: #d7afd7; }.xterm-dom-renderer-owner-1 .xterm-fg-183 { color: #d7afff; }.xterm-dom-renderer-owner-1 .xterm-fg-183.xterm-dim { color: #d7afff80; }.xterm-dom-renderer-owner-1 .xterm-bg-183 { background-color: #d7afff; }.xterm-dom-renderer-owner-1 .xterm-fg-184 { color: #d7d700; }.xterm-dom-renderer-owner-1 .xterm-fg-184.xterm-dim { color: #d7d70080; }.xterm-dom-renderer-owner-1 .xterm-bg-184 { background-color: #d7d700; }.xterm-dom-renderer-owner-1 .xterm-fg-185 { color: #d7d75f; }.xterm-dom-renderer-owner-1 .xterm-fg-185.xterm-dim { color: #d7d75f80; }.xterm-dom-renderer-owner-1 .xterm-bg-185 { background-color: #d7d75f; }.xterm-dom-renderer-owner-1 .xterm-fg-186 { color: #d7d787; }.xterm-dom-renderer-owner-1 .xterm-fg-186.xterm-dim { color: #d7d78780; }.xterm-dom-renderer-owner-1 .xterm-bg-186 { background-color: #d7d787; }.xterm-dom-renderer-owner-1 .xterm-fg-187 { color: #d7d7af; }.xterm-dom-renderer-owner-1 .xterm-fg-187.xterm-dim { color: #d7d7af80; }.xterm-dom-renderer-owner-1 .xterm-bg-187 { background-color: #d7d7af; }.xterm-dom-renderer-owner-1 .xterm-fg-188 { color: #d7d7d7; }.xterm-dom-renderer-owner-1 .xterm-fg-188.xterm-dim { color: #d7d7d780; }.xterm-dom-renderer-owner-1 .xterm-bg-188 { background-color: #d7d7d7; }.xterm-dom-renderer-owner-1 .xterm-fg-189 { color: #d7d7ff; }.xterm-dom-renderer-owner-1 .xterm-fg-189.xterm-dim { color: #d7d7ff80; }.xterm-dom-renderer-owner-1 .xterm-bg-189 { background-color: #d7d7ff; }.xterm-dom-renderer-owner-1 .xterm-fg-190 { color: #d7ff00; }.xterm-dom-renderer-owner-1 .xterm-fg-190.xterm-dim { color: #d7ff0080; }.xterm-dom-renderer-owner-1 .xterm-bg-190 { background-color: #d7ff00; }.xterm-dom-renderer-owner-1 .xterm-fg-191 { color: #d7ff5f; }.xterm-dom-renderer-owner-1 .xterm-fg-191.xterm-dim { color: #d7ff5f80; }.xterm-dom-renderer-owner-1 .xterm-bg-191 { background-color: #d7ff5f; }.xterm-dom-renderer-owner-1 .xterm-fg-192 { color: #d7ff87; }.xterm-dom-renderer-owner-1 .xterm-fg-192.xterm-dim { color: #d7ff8780; }.xterm-dom-renderer-owner-1 .xterm-bg-192 { background-color: #d7ff87; }.xterm-dom-renderer-owner-1 .xterm-fg-193 { color: #d7ffaf; }.xterm-dom-renderer-owner-1 .xterm-fg-193.xterm-dim { color: #d7ffaf80; }.xterm-dom-renderer-owner-1 .xterm-bg-193 { background-color: #d7ffaf; }.xterm-dom-renderer-owner-1 .xterm-fg-194 { color: #d7ffd7; }.xterm-dom-renderer-owner-1 .xterm-fg-194.xterm-dim { color: #d7ffd780; }.xterm-dom-renderer-owner-1 .xterm-bg-194 { background-color: #d7ffd7; }.xterm-dom-renderer-owner-1 .xterm-fg-195 { color: #d7ffff; }.xterm-dom-renderer-owner-1 .xterm-fg-195.xterm-dim { color: #d7ffff80; }.xterm-dom-renderer-owner-1 .xterm-bg-195 { background-color: #d7ffff; }.xterm-dom-renderer-owner-1 .xterm-fg-196 { color: #ff0000; }.xterm-dom-renderer-owner-1 .xterm-fg-196.xterm-dim { color: #ff000080; }.xterm-dom-renderer-owner-1 .xterm-bg-196 { background-color: #ff0000; }.xterm-dom-renderer-owner-1 .xterm-fg-197 { color: #ff005f; }.xterm-dom-renderer-owner-1 .xterm-fg-197.xterm-dim { color: #ff005f80; }.xterm-dom-renderer-owner-1 .xterm-bg-197 { background-color: #ff005f; }.xterm-dom-renderer-owner-1 .xterm-fg-198 { color: #ff0087; }.xterm-dom-renderer-owner-1 .xterm-fg-198.xterm-dim { color: #ff008780; }.xterm-dom-renderer-owner-1 .xterm-bg-198 { background-color: #ff0087; }.xterm-dom-renderer-owner-1 .xterm-fg-199 { color: #ff00af; }.xterm-dom-renderer-owner-1 .xterm-fg-199.xterm-dim { color: #ff00af80; }.xterm-dom-renderer-owner-1 .xterm-bg-199 { background-color: #ff00af; }.xterm-dom-renderer-owner-1 .xterm-fg-200 { color: #ff00d7; }.xterm-dom-renderer-owner-1 .xterm-fg-200.xterm-dim { color: #ff00d780; }.xterm-dom-renderer-owner-1 .xterm-bg-200 { background-color: #ff00d7; }.xterm-dom-renderer-owner-1 .xterm-fg-201 { color: #ff00ff; }.xterm-dom-renderer-owner-1 .xterm-fg-201.xterm-dim { color: #ff00ff80; }.xterm-dom-renderer-owner-1 .xterm-bg-201 { background-color: #ff00ff; }.xterm-dom-renderer-owner-1 .xterm-fg-202 { color: #ff5f00; }.xterm-dom-renderer-owner-1 .xterm-fg-202.xterm-dim { color: #ff5f0080; }.xterm-dom-renderer-owner-1 .xterm-bg-202 { background-color: #ff5f00; }.xterm-dom-renderer-owner-1 .xterm-fg-203 { color: #ff5f5f; }.xterm-dom-renderer-owner-1 .xterm-fg-203.xterm-dim { color: #ff5f5f80; }.xterm-dom-renderer-owner-1 .xterm-bg-203 { background-color: #ff5f5f; }.xterm-dom-renderer-owner-1 .xterm-fg-204 { color: #ff5f87; }.xterm-dom-renderer-owner-1 .xterm-fg-204.xterm-dim { color: #ff5f8780; }.xterm-dom-renderer-owner-1 .xterm-bg-204 { background-color: #ff5f87; }.xterm-dom-renderer-owner-1 .xterm-fg-205 { color: #ff5faf; }.xterm-dom-renderer-owner-1 .xterm-fg-205.xterm-dim { color: #ff5faf80; }.xterm-dom-renderer-owner-1 .xterm-bg-205 { background-color: #ff5faf; }.xterm-dom-renderer-owner-1 .xterm-fg-206 { color: #ff5fd7; }.xterm-dom-renderer-owner-1 .xterm-fg-206.xterm-dim { color: #ff5fd780; }.xterm-dom-renderer-owner-1 .xterm-bg-206 { background-color: #ff5fd7; }.xterm-dom-renderer-owner-1 .xterm-fg-207 { color: #ff5fff; }.xterm-dom-renderer-owner-1 .xterm-fg-207.xterm-dim { color: #ff5fff80; }.xterm-dom-renderer-owner-1 .xterm-bg-207 { background-color: #ff5fff; }.xterm-dom-renderer-owner-1 .xterm-fg-208 { color: #ff8700; }.xterm-dom-renderer-owner-1 .xterm-fg-208.xterm-dim { color: #ff870080; }.xterm-dom-renderer-owner-1 .xterm-bg-208 { background-color: #ff8700; }.xterm-dom-renderer-owner-1 .xterm-fg-209 { color: #ff875f; }.xterm-dom-renderer-owner-1 .xterm-fg-209.xterm-dim { color: #ff875f80; }.xterm-dom-renderer-owner-1 .xterm-bg-209 { background-color: #ff875f; }.xterm-dom-renderer-owner-1 .xterm-fg-210 { color: #ff8787; }.xterm-dom-renderer-owner-1 .xterm-fg-210.xterm-dim { color: #ff878780; }.xterm-dom-renderer-owner-1 .xterm-bg-210 { background-color: #ff8787; }.xterm-dom-renderer-owner-1 .xterm-fg-211 { color: #ff87af; }.xterm-dom-renderer-owner-1 .xterm-fg-211.xterm-dim { color: #ff87af80; }.xterm-dom-renderer-owner-1 .xterm-bg-211 { background-color: #ff87af; }.xterm-dom-renderer-owner-1 .xterm-fg-212 { color: #ff87d7; }.xterm-dom-renderer-owner-1 .xterm-fg-212.xterm-dim { color: #ff87d780; }.xterm-dom-renderer-owner-1 .xterm-bg-212 { background-color: #ff87d7; }.xterm-dom-renderer-owner-1 .xterm-fg-213 { color: #ff87ff; }.xterm-dom-renderer-owner-1 .xterm-fg-213.xterm-dim { color: #ff87ff80; }.xterm-dom-renderer-owner-1 .xterm-bg-213 { background-color: #ff87ff; }.xterm-dom-renderer-owner-1 .xterm-fg-214 { color: #ffaf00; }.xterm-dom-renderer-owner-1 .xterm-fg-214.xterm-dim { color: #ffaf0080; }.xterm-dom-renderer-owner-1 .xterm-bg-214 { background-color: #ffaf00; }.xterm-dom-renderer-owner-1 .xterm-fg-215 { color: #ffaf5f; }.xterm-dom-renderer-owner-1 .xterm-fg-215.xterm-dim { color: #ffaf5f80; }.xterm-dom-renderer-owner-1 .xterm-bg-215 { background-color: #ffaf5f; }.xterm-dom-renderer-owner-1 .xterm-fg-216 { color: #ffaf87; }.xterm-dom-renderer-owner-1 .xterm-fg-216.xterm-dim { color: #ffaf8780; }.xterm-dom-renderer-owner-1 .xterm-bg-216 { background-color: #ffaf87; }.xterm-dom-renderer-owner-1 .xterm-fg-217 { color: #ffafaf; }.xterm-dom-renderer-owner-1 .xterm-fg-217.xterm-dim { color: #ffafaf80; }.xterm-dom-renderer-owner-1 .xterm-bg-217 { background-color: #ffafaf; }.xterm-dom-renderer-owner-1 .xterm-fg-218 { color: #ffafd7; }.xterm-dom-renderer-owner-1 .xterm-fg-218.xterm-dim { color: #ffafd780; }.xterm-dom-renderer-owner-1 .xterm-bg-218 { background-color: #ffafd7; }.xterm-dom-renderer-owner-1 .xterm-fg-219 { color: #ffafff; }.xterm-dom-renderer-owner-1 .xterm-fg-219.xterm-dim { color: #ffafff80; }.xterm-dom-renderer-owner-1 .xterm-bg-219 { background-color: #ffafff; }.xterm-dom-renderer-owner-1 .xterm-fg-220 { color: #ffd700; }.xterm-dom-renderer-owner-1 .xterm-fg-220.xterm-dim { color: #ffd70080; }.xterm-dom-renderer-owner-1 .xterm-bg-220 { background-color: #ffd700; }.xterm-dom-renderer-owner-1 .xterm-fg-221 { color: #ffd75f; }.xterm-dom-renderer-owner-1 .xterm-fg-221.xterm-dim { color: #ffd75f80; }.xterm-dom-renderer-owner-1 .xterm-bg-221 { background-color: #ffd75f; }.xterm-dom-renderer-owner-1 .xterm-fg-222 { color: #ffd787; }.xterm-dom-renderer-owner-1 .xterm-fg-222.xterm-dim { color: #ffd78780; }.xterm-dom-renderer-owner-1 .xterm-bg-222 { background-color: #ffd787; }.xterm-dom-renderer-owner-1 .xterm-fg-223 { color: #ffd7af; }.xterm-dom-renderer-owner-1 .xterm-fg-223.xterm-dim { color: #ffd7af80; }.xterm-dom-renderer-owner-1 .xterm-bg-223 { background-color: #ffd7af; }.xterm-dom-renderer-owner-1 .xterm-fg-224 { color: #ffd7d7; }.xterm-dom-renderer-owner-1 .xterm-fg-224.xterm-dim { color: #ffd7d780; }.xterm-dom-renderer-owner-1 .xterm-bg-224 { background-color: #ffd7d7; }.xterm-dom-renderer-owner-1 .xterm-fg-225 { color: #ffd7ff; }.xterm-dom-renderer-owner-1 .xterm-fg-225.xterm-dim { color: #ffd7ff80; }.xterm-dom-renderer-owner-1 .xterm-bg-225 { background-color: #ffd7ff; }.xterm-dom-renderer-owner-1 .xterm-fg-226 { color: #ffff00; }.xterm-dom-renderer-owner-1 .xterm-fg-226.xterm-dim { color: #ffff0080; }.xterm-dom-renderer-owner-1 .xterm-bg-226 { background-color: #ffff00; }.xterm-dom-renderer-owner-1 .xterm-fg-227 { color: #ffff5f; }.xterm-dom-renderer-owner-1 .xterm-fg-227.xterm-dim { color: #ffff5f80; }.xterm-dom-renderer-owner-1 .xterm-bg-227 { background-color: #ffff5f; }.xterm-dom-renderer-owner-1 .xterm-fg-228 { color: #ffff87; }.xterm-dom-renderer-owner-1 .xterm-fg-228.xterm-dim { color: #ffff8780; }.xterm-dom-renderer-owner-1 .xterm-bg-228 { background-color: #ffff87; }.xterm-dom-renderer-owner-1 .xterm-fg-229 { color: #ffffaf; }.xterm-dom-renderer-owner-1 .xterm-fg-229.xterm-dim { color: #ffffaf80; }.xterm-dom-renderer-owner-1 .xterm-bg-229 { background-color: #ffffaf; }.xterm-dom-renderer-owner-1 .xterm-fg-230 { color: #ffffd7; }.xterm-dom-renderer-owner-1 .xterm-fg-230.xterm-dim { color: #ffffd780; }.xterm-dom-renderer-owner-1 .xterm-bg-230 { background-color: #ffffd7; }.xterm-dom-renderer-owner-1 .xterm-fg-231 { color: #ffffff; }.xterm-dom-renderer-owner-1 .xterm-fg-231.xterm-dim { color: #ffffff80; }.xterm-dom-renderer-owner-1 .xterm-bg-231 { background-color: #ffffff; }.xterm-dom-renderer-owner-1 .xterm-fg-232 { color: #080808; }.xterm-dom-renderer-owner-1 .xterm-fg-232.xterm-dim { color: #08080880; }.xterm-dom-renderer-owner-1 .xterm-bg-232 { background-color: #080808; }.xterm-dom-renderer-owner-1 .xterm-fg-233 { color: #121212; }.xterm-dom-renderer-owner-1 .xterm-fg-233.xterm-dim { color: #12121280; }.xterm-dom-renderer-owner-1 .xterm-bg-233 { background-color: #121212; }.xterm-dom-renderer-owner-1 .xterm-fg-234 { color: #1c1c1c; }.xterm-dom-renderer-owner-1 .xterm-fg-234.xterm-dim { color: #1c1c1c80; }.xterm-dom-renderer-owner-1 .xterm-bg-234 { background-color: #1c1c1c; }.xterm-dom-renderer-owner-1 .xterm-fg-235 { color: #262626; }.xterm-dom-renderer-owner-1 .xterm-fg-235.xterm-dim { color: #26262680; }.xterm-dom-renderer-owner-1 .xterm-bg-235 { background-color: #262626; }.xterm-dom-renderer-owner-1 .xterm-fg-236 { color: #303030; }.xterm-dom-renderer-owner-1 .xterm-fg-236.xterm-dim { color: #30303080; }.xterm-dom-renderer-owner-1 .xterm-bg-236 { background-color: #303030; }.xterm-dom-renderer-owner-1 .xterm-fg-237 { color: #3a3a3a; }.xterm-dom-renderer-owner-1 .xterm-fg-237.xterm-dim { color: #3a3a3a80; }.xterm-dom-renderer-owner-1 .xterm-bg-237 { background-color: #3a3a3a; }.xterm-dom-renderer-owner-1 .xterm-fg-238 { color: #444444; }.xterm-dom-renderer-owner-1 .xterm-fg-238.xterm-dim { color: #44444480; }.xterm-dom-renderer-owner-1 .xterm-bg-238 { background-color: #444444; }.xterm-dom-renderer-owner-1 .xterm-fg-239 { color: #4e4e4e; }.xterm-dom-renderer-owner-1 .xterm-fg-239.xterm-dim { color: #4e4e4e80; }.xterm-dom-renderer-owner-1 .xterm-bg-239 { background-color: #4e4e4e; }.xterm-dom-renderer-owner-1 .xterm-fg-240 { color: #585858; }.xterm-dom-renderer-owner-1 .xterm-fg-240.xterm-dim { color: #58585880; }.xterm-dom-renderer-owner-1 .xterm-bg-240 { background-color: #585858; }.xterm-dom-renderer-owner-1 .xterm-fg-241 { color: #626262; }.xterm-dom-renderer-owner-1 .xterm-fg-241.xterm-dim { color: #62626280; }.xterm-dom-renderer-owner-1 .xterm-bg-241 { background-color: #626262; }.xterm-dom-renderer-owner-1 .xterm-fg-242 { color: #6c6c6c; }.xterm-dom-renderer-owner-1 .xterm-fg-242.xterm-dim { color: #6c6c6c80; }.xterm-dom-renderer-owner-1 .xterm-bg-242 { background-color: #6c6c6c; }.xterm-dom-renderer-owner-1 .xterm-fg-243 { color: #767676; }.xterm-dom-renderer-owner-1 .xterm-fg-243.xterm-dim { color: #76767680; }.xterm-dom-renderer-owner-1 .xterm-bg-243 { background-color: #767676; }.xterm-dom-renderer-owner-1 .xterm-fg-244 { color: #808080; }.xterm-dom-renderer-owner-1 .xterm-fg-244.xterm-dim { color: #80808080; }.xterm-dom-renderer-owner-1 .xterm-bg-244 { background-color: #808080; }.xterm-dom-renderer-owner-1 .xterm-fg-245 { color: #8a8a8a; }.xterm-dom-renderer-owner-1 .xterm-fg-245.xterm-dim { color: #8a8a8a80; }.xterm-dom-renderer-owner-1 .xterm-bg-245 { background-color: #8a8a8a; }.xterm-dom-renderer-owner-1 .xterm-fg-246 { color: #949494; }.xterm-dom-renderer-owner-1 .xterm-fg-246.xterm-dim { color: #94949480; }.xterm-dom-renderer-owner-1 .xterm-bg-246 { background-color: #949494; }.xterm-dom-renderer-owner-1 .xterm-fg-247 { color: #9e9e9e; }.xterm-dom-renderer-owner-1 .xterm-fg-247.xterm-dim { color: #9e9e9e80; }.xterm-dom-renderer-owner-1 .xterm-bg-247 { background-color: #9e9e9e; }.xterm-dom-renderer-owner-1 .xterm-fg-248 { color: #a8a8a8; }.xterm-dom-renderer-owner-1 .xterm-fg-248.xterm-dim { color: #a8a8a880; }.xterm-dom-renderer-owner-1 .xterm-bg-248 { background-color: #a8a8a8; }.xterm-dom-renderer-owner-1 .xterm-fg-249 { color: #b2b2b2; }.xterm-dom-renderer-owner-1 .xterm-fg-249.xterm-dim { color: #b2b2b280; }.xterm-dom-renderer-owner-1 .xterm-bg-249 { background-color: #b2b2b2; }.xterm-dom-renderer-owner-1 .xterm-fg-250 { color: #bcbcbc; }.xterm-dom-renderer-owner-1 .xterm-fg-250.xterm-dim { color: #bcbcbc80; }.xterm-dom-renderer-owner-1 .xterm-bg-250 { background-color: #bcbcbc; }.xterm-dom-renderer-owner-1 .xterm-fg-251 { color: #c6c6c6; }.xterm-dom-renderer-owner-1 .xterm-fg-251.xterm-dim { color: #c6c6c680; }.xterm-dom-renderer-owner-1 .xterm-bg-251 { background-color: #c6c6c6; }.xterm-dom-renderer-owner-1 .xterm-fg-252 { color: #d0d0d0; }.xterm-dom-renderer-owner-1 .xterm-fg-252.xterm-dim { color: #d0d0d080; }.xterm-dom-renderer-owner-1 .xterm-bg-252 { background-color: #d0d0d0; }.xterm-dom-renderer-owner-1 .xterm-fg-253 { color: #dadada; }.xterm-dom-renderer-owner-1 .xterm-fg-253.xterm-dim { color: #dadada80; }.xterm-dom-renderer-owner-1 .xterm-bg-253 { background-color: #dadada; }.xterm-dom-renderer-owner-1 .xterm-fg-254 { color: #e4e4e4; }.xterm-dom-renderer-owner-1 .xterm-fg-254.xterm-dim { color: #e4e4e480; }.xterm-dom-renderer-owner-1 .xterm-bg-254 { background-color: #e4e4e4; }.xterm-dom-renderer-owner-1 .xterm-fg-255 { color: #eeeeee; }.xterm-dom-renderer-owner-1 .xterm-fg-255.xterm-dim { color: #eeeeee80; }.xterm-dom-renderer-owner-1 .xterm-bg-255 { background-color: #eeeeee; }.xterm-dom-renderer-owner-1 .xterm-fg-257 { color: #000000; }.xterm-dom-renderer-owner-1 .xterm-fg-257.xterm-dim { color: #00000080; }.xterm-dom-renderer-owner-1 .xterm-bg-257 { background-color: #ffffff; }
                  </style>
                  <div
                    aria-hidden="true"
                    class="xterm-rows"
                    style="line-height: normal; letter-spacing: 0px;"
                  >
                    <div
                      style="width: 0px; height: 0px; line-height: 0px; overflow: hidden;"
                    >
                      <span>
                        first log entry
                      </span>
                    </div>
                    <div
                      style="width: 0px; height: 0px; line-height: 0px; overflow: hidden;"
                    >
                      <span>
                        second log entry
                      </span>
                    </div>
                    <div
                      style="width: 0px; height: 0px; line-height: 0px; overflow: hidden;"
                    >
                      <span>
                        third log entry
                      </span>
                    </div>
                    <div
                      style="width: 0px; height: 0px; line-height: 0px; overflow: hidden;"
                    >
                      <span>
                        end of log stream
                      </span>
                      <span>
                         
                      </span>
                    </div>
                    <div
                      style="width: 0px; height: 0px; line-height: 0px; overflow: hidden;"
                    />
                    <div
                      style="width: 0px; height: 0px; line-height: 0px; overflow: hidden;"
                    />
                    <div
                      style="width: 0px; height: 0px; line-height: 0px; overflow: hidden;"
                    />
                    <div
                      style="width: 0px; height: 0px; line-height: 0px; overflow: hidden;"
                    />
                    <div
                      style="width: 0px; height: 0px; line-height: 0px; overflow: hidden;"
                    />
                    <div
                      style="width: 0px; height: 0px; line-height: 0px; overflow: hidden;"
                    />
                    <div
                      style="width: 0px; height: 0px; line-height: 0px; overflow: hidden;"
                    />
                    <div
                      style="width: 0px; height: 0px; line-height: 0px; overflow: hidden;"
                    />
                    <div
                      style="width: 0px; height: 0px; line-height: 0px; overflow: hidden;"
                    />
                    <div
                      style="width: 0px; height: 0px; line-height: 0px; overflow: hidden;"
                    />
                    <div
                      style="width: 0px; height: 0px; line-height: 0px; overflow: hidden;"
                    />
                    <div
                      style="width: 0px; height: 0px; line-height: 0px; overflow: hidden;"
                    />
                    <div
                      style="width: 0px; height: 0px; line-height: 0px; overflow: hidden;"
                    />
                    <div
                      style="width: 0px; height: 0px; line-height: 0px; overflow: hidden;"
                    />
                    <div
                      style="width: 0px; height: 0px; line-height: 0px; overflow: hidden;"
                    />
                    <div
                      style="width: 0px; height: 0px; line-height: 0px; overflow: hidden;"
                    />
                    <div
                      style="width: 0px; height: 0px; line-height: 0px; overflow: hidden;"
                    />
                    <div
                      style="width: 0px; height: 0px; line-height: 0px; overflow: hidden;"
                    />
                    <div
                      style="width: 0px; height: 0px; line-height: 0px; overflow: hidden;"
                    />
                    <div
                      style="width: 0px; height: 0px; line-height: 0px; overflow: hidden;"
                    />
                    <div
                      style="width: 0px; height: 0px; line-height: 0px; overflow: hidden;"
                    />
                    <div
                      style="width: 0px; height: 0px; line-height: 0px; overflow: hidden;"
                    />
                    <div
                      style="width: 0px; height: 0px; line-height: 0px; overflow: hidden;"
                    />
                    <div
                      style="width: 0px; height: 0px; line-height: 0px; overflow: hidden;"
                    />
                    <div
                      style="width: 0px; height: 0px; line-height: 0px; overflow: hidden;"
                    />
                    <div
                      style="width: 0px; height: 0px; line-height: 0px; overflow: hidden;"
                    />
                  </div>
                  <div
                    aria-hidden="true"
                    class="xterm-selection"
                  />
                  <div
                    class="xterm-decoration-container"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      data-testid="sentinelEnd"
      tabindex="0"
    />
  </div>
</body>