<body>
  <div>
     
    <div
      class="MuiBox-root css-r4bbqj"
    >
      <h6
        class="MuiTypography-root MuiTypography-h6 css-k6p83p-MuiTypography-root"
      >
        Some Content Area
      </h6>
      <p
        class="MuiTypography-root MuiTypography-body2 MuiTypography-gutterBottom css-1hgh5ir-MuiTypography-root"
      >
        Imagine data is being fetched for this section.
      </p>
      <div
        class="MuiBox-root css-5cned0"
      >
        <span
          class="MuiCircularProgress-root MuiCircularProgress-indeterminate MuiCircularProgress-colorPrimary css-1g0vz9s-MuiCircularProgress-root"
          role="progressbar"
          style="width: 30px; height: 30px;"
          title="Fetching section data..."
        >
          <svg
            class="MuiCircularProgress-svg css-1idz92c-MuiCircularProgress-svg"
            viewBox="22 22 44 44"
          >
            <circle
              class="MuiCircularProgress-circle MuiCircularProgress-circleIndeterminate css-176wh8e-MuiCircularProgress-circle"
              cx="44"
              cy="44"
              fill="none"
              r="20.2"
              stroke-width="3.6"
            />
          </svg>
        </span>
      </div>
      <p
        class="MuiTypography-root MuiTypography-body2 css-ab4e19-MuiTypography-root"
        style="margin-top: 1rem;"
      >
        More content can be around the loader.
      </p>
    </div>
  </div>
</body>