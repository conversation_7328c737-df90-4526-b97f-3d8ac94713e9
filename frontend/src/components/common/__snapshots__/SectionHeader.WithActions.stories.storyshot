<body>
  <div>
     
    <div
      class="MuiGrid-root MuiGrid-container MuiGrid-spacing-xs-2 css-1ts0dnm-MuiGrid-root"
    >
      <div
        class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
      >
        <div
          class="MuiBox-root css-70qvj9"
        >
          <h2
            class="MuiTypography-root MuiTypography-h2 MuiTypography-noWrap css-m5vcfd-MuiTypography-root"
          >
            Section With Actions
          </h2>
          <div
            class="MuiBox-root css-ldp2l3"
          />
        </div>
      </div>
      <div
        class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
      >
        <div
          class="MuiGrid-root MuiGrid-container MuiGrid-item css-ztq4zc-MuiGrid-root"
        >
          <div
            class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
          >
            <button
              class="MuiButtonBase-root MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeSmall MuiButton-outlinedSizeSmall MuiButton-colorPrimary MuiButton-disableElevation MuiButton-root MuiButton-outlined MuiButton-outlinedPrimary MuiButton-sizeSmall MuiButton-outlinedSizeSmall MuiButton-colorPrimary MuiButton-disableElevation css-irzb5y-MuiButtonBase-root-MuiButton-root"
              tabindex="0"
              type="button"
            >
              Edit
              <span
                class="MuiTouchRipple-root css-8je8zh-MuiTouchRipple-root"
              />
            </button>
          </div>
          <div
            class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
          >
            <button
              class="MuiButtonBase-root MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeSmall MuiButton-containedSizeSmall MuiButton-colorPrimary MuiButton-disableElevation MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeSmall MuiButton-containedSizeSmall MuiButton-colorPrimary MuiButton-disableElevation css-g84ite-MuiButtonBase-root-MuiButton-root"
              tabindex="0"
              type="button"
            >
              Delete
              <span
                class="MuiTouchRipple-root css-8je8zh-MuiTouchRipple-root"
              />
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</body>