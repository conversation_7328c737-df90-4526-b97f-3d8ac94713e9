<body>
  <div>
    <div
      class="MuiSnackbar-root MuiSnackbar-anchorOriginBottomRight css-1mdvdtb-MuiSnackbar-root"
      role="presentation"
    >
      <div
        aria-describedby="updatePopup"
        class="MuiPaper-root MuiPaper-outlined MuiSnackbarContent-root css-1cboije-MuiPaper-root-MuiSnackbarContent-root"
        direction="up"
        role="alert"
        style="opacity: 1; transform: none; transition: opacity 225ms cubic-bezier(0.4, 0, 0.2, 1) 0ms,transform 150ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;"
      >
        <div
          class="MuiSnackbarContent-message css-1exqwzz-MuiSnackbarContent-message"
        >
          An update is available
        </div>
        <div
          class="MuiSnackbarContent-action css-1kr9x0n-MuiSnackbarContent-action"
        >
          <div
            class="MuiBox-root css-70qvj9"
          >
            <div
              class="MuiBox-root css-1aisqbv"
            >
              <button
                class="MuiButtonBase-root MuiButton-root MuiButton-text MuiButton-textPrimary MuiButton-sizeMedium MuiButton-textSizeMedium MuiButton-colorPrimary MuiButton-disableElevation MuiButton-root MuiButton-text MuiButton-textPrimary MuiButton-sizeMedium MuiButton-textSizeMedium MuiButton-colorPrimary MuiButton-disableElevation css-5534ty-MuiButtonBase-root-MuiButton-root"
                style="text-transform: none;"
                tabindex="0"
                type="button"
              >
                Read more
                <span
                  class="MuiTouchRipple-root css-8je8zh-MuiTouchRipple-root"
                />
              </button>
            </div>
            <div
              class="MuiBox-root css-1itv5e3"
            >
              <button
                class="MuiButtonBase-root MuiButton-root MuiButton-text MuiButton-textPrimary MuiButton-sizeMedium MuiButton-textSizeMedium MuiButton-colorPrimary MuiButton-disableElevation MuiButton-root MuiButton-text MuiButton-textPrimary MuiButton-sizeMedium MuiButton-textSizeMedium MuiButton-colorPrimary MuiButton-disableElevation css-5534ty-MuiButtonBase-root-MuiButton-root"
                style="color: rgb(255, 242, 0);"
                tabindex="0"
                type="button"
              >
                <span
                  class="MuiTouchRipple-root css-8je8zh-MuiTouchRipple-root"
                />
              </button>
            </div>
            <div
              class="MuiBox-root css-0"
            >
              <button
                class="MuiButtonBase-root MuiButton-root MuiButton-text MuiButton-textPrimary MuiButton-sizeMedium MuiButton-textSizeMedium MuiButton-colorPrimary MuiButton-disableElevation MuiButton-root MuiButton-text MuiButton-textPrimary MuiButton-sizeMedium MuiButton-textSizeMedium MuiButton-colorPrimary MuiButton-disableElevation css-5534ty-MuiButtonBase-root-MuiButton-root"
                style="color: rgb(255, 242, 0);"
                tabindex="0"
                type="button"
              >
                Dismiss
                <span
                  class="MuiTouchRipple-root css-8je8zh-MuiTouchRipple-root"
                />
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</body>