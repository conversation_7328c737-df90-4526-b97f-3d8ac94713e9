<body>
  <div>
    <div
      class="MuiBox-root css-0"
    >
      <p
        class="MuiTypography-root MuiTypography-body1 css-1ezega9-MuiTypography-root"
      >
        Test changing the page and rows per page.
      </p>
      <p
        class="MuiTypography-root MuiTypography-body1 css-1ezega9-MuiTypography-root"
      >
        <b>
          Current URL search:
        </b>
         
        ?p=2
      </p>
      <div
        class="MuiBox-root css-1ipmh7v"
      >
        <div
          class="MuiCollapse-root MuiCollapse-vertical MuiCollapse-hidden css-bz4dnt-MuiCollapse-root"
          style="min-height: 0px;"
        >
          <div
            class="MuiCollapse-wrapper MuiCollapse-vertical css-smkl36-MuiCollapse-wrapper"
          >
            <div
              class="MuiCollapse-wrapperInner MuiCollapse-vertical css-9l5vo-MuiCollapse-wrapperInner"
            >
              <div
                class="MuiPaper-root MuiPaper-outlined MuiPaper-rounded MuiAlert-root MuiAlert-colorInfo MuiAlert-standardInfo Mui<PERSON>lert-standard css-s0ueq2-MuiPaper-root-MuiAlert-root"
                role="alert"
              >
                <div
                  class="MuiAlert-message css-1pxa9xg-MuiAlert-message"
                >
                  <div
                    class="MuiStack-root css-5kul5x-MuiStack-root"
                  >
                    <div
                      class="MuiBox-root css-k008qs"
                    >
                       
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          class="Mui-ToolbarDropZone MuiBox-root css-1fwjva3"
          style="opacity: 0; visibility: hidden;"
        >
          <p
            class="MuiTypography-root MuiTypography-body1 css-18b7jph-MuiTypography-root"
          >
            Drop to group by 
          </p>
        </div>
        <div
          class="MuiBox-root css-zrlv9q"
        >
          <span />
          <div
            class="MuiBox-root css-1p0wbhh"
          >
            <div
              class="MuiBox-root css-di3982"
            >
              <button
                aria-label="Show/Hide search"
                class="MuiButtonBase-root MuiIconButton-root MuiIconButton-sizeMedium css-whz9ym-MuiButtonBase-root-MuiIconButton-root"
                data-mui-internal-clone-element="true"
                tabindex="0"
                type="button"
              >
                <svg
                  aria-hidden="true"
                  class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-i4bv87-MuiSvgIcon-root"
                  data-testid="SearchIcon"
                  focusable="false"
                  viewBox="0 0 24 24"
                >
                  <path
                    d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14"
                  />
                </svg>
                <span
                  class="MuiTouchRipple-root css-8je8zh-MuiTouchRipple-root"
                />
              </button>
              <button
                aria-label="Show/Hide filters"
                class="MuiButtonBase-root MuiIconButton-root MuiIconButton-sizeMedium css-whz9ym-MuiButtonBase-root-MuiIconButton-root"
                data-mui-internal-clone-element="true"
                tabindex="0"
                type="button"
              >
                <svg
                  aria-hidden="true"
                  class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-i4bv87-MuiSvgIcon-root"
                  data-testid="FilterListIcon"
                  focusable="false"
                  viewBox="0 0 24 24"
                >
                  <path
                    d="M10 18h4v-2h-4zM3 6v2h18V6zm3 7h12v-2H6z"
                  />
                </svg>
                <span
                  class="MuiTouchRipple-root css-8je8zh-MuiTouchRipple-root"
                />
              </button>
              <button
                aria-label="Show/Hide columns"
                class="MuiButtonBase-root MuiIconButton-root MuiIconButton-sizeMedium css-whz9ym-MuiButtonBase-root-MuiIconButton-root"
                data-mui-internal-clone-element="true"
                tabindex="0"
                type="button"
              >
                <svg
                  aria-hidden="true"
                  class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-i4bv87-MuiSvgIcon-root"
                  data-testid="ViewColumnIcon"
                  focusable="false"
                  viewBox="0 0 24 24"
                >
                  <path
                    d="M14.67 5v14H9.33V5zm1 14H21V5h-5.33zm-7.34 0V5H3v14z"
                  />
                </svg>
                <span
                  class="MuiTouchRipple-root css-8je8zh-MuiTouchRipple-root"
                />
              </button>
            </div>
          </div>
        </div>
      </div>
      <table
        class="MuiTable-root css-dq3jrr-MuiTable-root"
      >
        <thead
          class="MuiTableHead-root css-1tmrira-MuiTableHead-root"
        >
          <tr
            class="css-1lqh5un"
          >
            <th
              aria-sort="none"
              class="MuiTableCell-root MuiTableCell-head MuiTableCell-alignLeft MuiTableCell-sizeMedium css-542uyz-MuiTableCell-root"
              colspan="1"
              data-can-sort="true"
              data-index="-1"
              scope="col"
            >
              <div
                class="Mui-TableHeadCell-Content MuiBox-root css-1w86f15"
              >
                <div
                  class="Mui-TableHeadCell-Content-Labels MuiBox-root css-68rqdf"
                >
                  <div
                    class="Mui-TableHeadCell-Content-Wrapper MuiBox-root css-lapokc"
                  >
                    Name
                  </div>
                  <span
                    aria-label="Sort by Name ascending"
                    class="MuiBadge-root css-1c32n2y-MuiBadge-root"
                    data-mui-internal-clone-element="true"
                  >
                    <span
                      aria-label="Sort by Name ascending"
                      class="MuiButtonBase-root MuiTableSortLabel-root Mui-active css-542clt-MuiButtonBase-root-MuiTableSortLabel-root"
                      role="button"
                      tabindex="0"
                    >
                      <svg
                        aria-hidden="true"
                        class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium MuiTableSortLabel-icon MuiTableSortLabel-iconDirectionAsc css-1vweko9-MuiSvgIcon-root-MuiTableSortLabel-icon"
                        data-testid="SyncAltIcon"
                        focusable="false"
                        style="transform: rotate(-90deg) scaleX(0.9) translateX(-1px);"
                        viewBox="0 0 24 24"
                      >
                        <path
                          d="m18 12 4-4-4-4v3H3v2h15zM6 12l-4 4 4 4v-3h15v-2H6z"
                        />
                      </svg>
                    </span>
                    <span
                      class="MuiBadge-badge MuiBadge-standard MuiBadge-invisible MuiBadge-anchorOriginTopRight MuiBadge-anchorOriginTopRightCircular MuiBadge-overlapCircular css-dniquu-MuiBadge-badge"
                    >
                      0
                    </span>
                  </span>
                </div>
                <div
                  class="Mui-TableHeadCell-Content-Actions MuiBox-root css-epvm6"
                />
              </div>
            </th>
            <th
              aria-sort="none"
              class="MuiTableCell-root MuiTableCell-head MuiTableCell-alignLeft MuiTableCell-sizeMedium css-11ihby4-MuiTableCell-root"
              colspan="1"
              data-can-sort="true"
              data-index="-1"
              scope="col"
            >
              <div
                class="Mui-TableHeadCell-Content MuiBox-root css-1w86f15"
              >
                <div
                  class="Mui-TableHeadCell-Content-Labels MuiBox-root css-68rqdf"
                >
                  <div
                    class="Mui-TableHeadCell-Content-Wrapper MuiBox-root css-lapokc"
                  >
                    Namespace
                  </div>
                  <span
                    aria-label="Sort by Namespace ascending"
                    class="MuiBadge-root css-1c32n2y-MuiBadge-root"
                    data-mui-internal-clone-element="true"
                  >
                    <span
                      aria-label="Sort by Namespace ascending"
                      class="MuiButtonBase-root MuiTableSortLabel-root Mui-active css-542clt-MuiButtonBase-root-MuiTableSortLabel-root"
                      role="button"
                      tabindex="0"
                    >
                      <svg
                        aria-hidden="true"
                        class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium MuiTableSortLabel-icon MuiTableSortLabel-iconDirectionAsc css-1vweko9-MuiSvgIcon-root-MuiTableSortLabel-icon"
                        data-testid="SyncAltIcon"
                        focusable="false"
                        style="transform: rotate(-90deg) scaleX(0.9) translateX(-1px);"
                        viewBox="0 0 24 24"
                      >
                        <path
                          d="m18 12 4-4-4-4v3H3v2h15zM6 12l-4 4 4 4v-3h15v-2H6z"
                        />
                      </svg>
                    </span>
                    <span
                      class="MuiBadge-badge MuiBadge-standard MuiBadge-invisible MuiBadge-anchorOriginTopRight MuiBadge-anchorOriginTopRightCircular MuiBadge-overlapCircular css-dniquu-MuiBadge-badge"
                    >
                      0
                    </span>
                  </span>
                </div>
                <div
                  class="Mui-TableHeadCell-Content-Actions MuiBox-root css-epvm6"
                />
              </div>
            </th>
            <th
              aria-sort="none"
              class="MuiTableCell-root MuiTableCell-head MuiTableCell-alignLeft MuiTableCell-sizeMedium css-9s5e2s-MuiTableCell-root"
              colspan="1"
              data-can-sort="true"
              data-index="-1"
              scope="col"
            >
              <div
                class="Mui-TableHeadCell-Content MuiBox-root css-1w86f15"
              >
                <div
                  class="Mui-TableHeadCell-Content-Labels MuiBox-root css-68rqdf"
                >
                  <div
                    class="Mui-TableHeadCell-Content-Wrapper MuiBox-root css-lapokc"
                  >
                    Number
                  </div>
                  <span
                    aria-label="Sort by Number descending"
                    class="MuiBadge-root css-1c32n2y-MuiBadge-root"
                    data-mui-internal-clone-element="true"
                  >
                    <span
                      aria-label="Sort by Number descending"
                      class="MuiButtonBase-root MuiTableSortLabel-root Mui-active css-542clt-MuiButtonBase-root-MuiTableSortLabel-root"
                      role="button"
                      tabindex="0"
                    >
                      <svg
                        aria-hidden="true"
                        class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium MuiTableSortLabel-icon MuiTableSortLabel-iconDirectionAsc css-1vweko9-MuiSvgIcon-root-MuiTableSortLabel-icon"
                        data-testid="SyncAltIcon"
                        focusable="false"
                        style="transform: rotate(-90deg) scaleX(0.9) translateX(-1px);"
                        viewBox="0 0 24 24"
                      >
                        <path
                          d="m18 12 4-4-4-4v3H3v2h15zM6 12l-4 4 4 4v-3h15v-2H6z"
                        />
                      </svg>
                    </span>
                    <span
                      class="MuiBadge-badge MuiBadge-standard MuiBadge-invisible MuiBadge-anchorOriginTopRight MuiBadge-anchorOriginTopRightCircular MuiBadge-overlapCircular css-dniquu-MuiBadge-badge"
                    >
                      0
                    </span>
                  </span>
                </div>
                <div
                  class="Mui-TableHeadCell-Content-Actions MuiBox-root css-epvm6"
                />
              </div>
            </th>
          </tr>
        </thead>
        <tbody
          class="css-1obf64m"
        >
          <tr
            class="css-1ospngb"
            data-selected="false"
          >
            <td
              class="MuiTableCell-root MuiTableCell-alignLeft MuiTableCell-sizeMedium css-1574zbf-MuiTableCell-root"
            >
              Name 0
            </td>
            <td
              class="MuiTableCell-root MuiTableCell-alignLeft MuiTableCell-sizeMedium css-2wxm0s-MuiTableCell-root"
            >
              Namespace 0
            </td>
            <td
              class="MuiTableCell-root MuiTableCell-alignLeft MuiTableCell-sizeMedium css-1qzqemr-MuiTableCell-root"
            >
              0
            </td>
          </tr>
          <tr
            class="css-1ospngb"
            data-selected="false"
          >
            <td
              class="MuiTableCell-root MuiTableCell-alignLeft MuiTableCell-sizeMedium css-1574zbf-MuiTableCell-root"
            >
              Name 1
            </td>
            <td
              class="MuiTableCell-root MuiTableCell-alignLeft MuiTableCell-sizeMedium css-2wxm0s-MuiTableCell-root"
            >
              Namespace 1
            </td>
            <td
              class="MuiTableCell-root MuiTableCell-alignLeft MuiTableCell-sizeMedium css-1qzqemr-MuiTableCell-root"
            >
              1
            </td>
          </tr>
          <tr
            class="css-1ospngb"
            data-selected="false"
          >
            <td
              class="MuiTableCell-root MuiTableCell-alignLeft MuiTableCell-sizeMedium css-1574zbf-MuiTableCell-root"
            >
              Name 2
            </td>
            <td
              class="MuiTableCell-root MuiTableCell-alignLeft MuiTableCell-sizeMedium css-2wxm0s-MuiTableCell-root"
            >
              Namespace 2
            </td>
            <td
              class="MuiTableCell-root MuiTableCell-alignLeft MuiTableCell-sizeMedium css-1qzqemr-MuiTableCell-root"
            >
              2
            </td>
          </tr>
          <tr
            class="css-1ospngb"
            data-selected="false"
          >
            <td
              class="MuiTableCell-root MuiTableCell-alignLeft MuiTableCell-sizeMedium css-1574zbf-MuiTableCell-root"
            >
              Name 3
            </td>
            <td
              class="MuiTableCell-root MuiTableCell-alignLeft MuiTableCell-sizeMedium css-2wxm0s-MuiTableCell-root"
            >
              Namespace 3
            </td>
            <td
              class="MuiTableCell-root MuiTableCell-alignLeft MuiTableCell-sizeMedium css-1qzqemr-MuiTableCell-root"
            >
              3
            </td>
          </tr>
          <tr
            class="css-1ospngb"
            data-selected="false"
          >
            <td
              class="MuiTableCell-root MuiTableCell-alignLeft MuiTableCell-sizeMedium css-1574zbf-MuiTableCell-root"
            >
              Name 4
            </td>
            <td
              class="MuiTableCell-root MuiTableCell-alignLeft MuiTableCell-sizeMedium css-2wxm0s-MuiTableCell-root"
            >
              Namespace 4
            </td>
            <td
              class="MuiTableCell-root MuiTableCell-alignLeft MuiTableCell-sizeMedium css-1qzqemr-MuiTableCell-root"
            >
              4
            </td>
          </tr>
        </tbody>
      </table>
      <div
        class="MuiBox-root css-1bxknjp"
      >
        <div
          class="MuiBox-root css-1llu0od"
        >
          <span />
          <div
            class="MuiBox-root css-qpxlqp"
          >
            <div
              class="MuiTablePagination-root MuiBox-root css-1brajfe"
            >
              <div
                class="MuiBox-root css-exd1zr"
              >
                <label
                  class="MuiFormLabel-root MuiInputLabel-root MuiInputLabel-animated MuiFormLabel-colorPrimary MuiInputLabel-root MuiInputLabel-animated css-17e6cjd-MuiFormLabel-root-MuiInputLabel-root"
                  for="mrt-rows-per-page"
                >
                  Rows per page
                </label>
                <div
                  class="MuiInputBase-root MuiInput-root MuiInputBase-colorPrimary  css-dnm9fb-MuiInputBase-root-MuiInput-root-MuiSelect-root"
                >
                  <div
                    aria-controls="test-id"
                    aria-expanded="false"
                    aria-haspopup="listbox"
                    class="MuiSelect-select MuiSelect-standard MuiInputBase-input MuiInput-input css-9t3t1q-MuiSelect-select-MuiInputBase-input-MuiInput-input"
                    role="combobox"
                    tabindex="0"
                  >
                    5
                  </div>
                  <input
                    aria-hidden="true"
                    aria-invalid="false"
                    class="MuiSelect-nativeInput css-yf8vq0-MuiSelect-nativeInput"
                    tabindex="-1"
                    value="5"
                  />
                  <svg
                    aria-hidden="true"
                    class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium MuiSelect-icon MuiSelect-iconStandard css-pqjvzy-MuiSvgIcon-root-MuiSelect-icon"
                    data-testid="ArrowDropDownIcon"
                    focusable="false"
                    viewBox="0 0 24 24"
                  >
                    <path
                      d="M7 10l5 5 5-5z"
                    />
                  </svg>
                </div>
              </div>
              <span
                class="MuiTypography-root MuiTypography-body2 MuiTypography-alignCenter css-htm6yz-MuiTypography-root"
              >
                1-5 of 50
              </span>
              <div
                class="MuiBox-root css-192dx0w"
              >
                <span
                  aria-label="Go to previous page"
                  class=""
                  data-mui-internal-clone-element="true"
                >
                  <button
                    aria-label="Go to previous page"
                    class="MuiButtonBase-root Mui-disabled MuiIconButton-root Mui-disabled MuiIconButton-sizeSmall css-hvz71z-MuiButtonBase-root-MuiIconButton-root"
                    disabled=""
                    tabindex="-1"
                    type="button"
                  >
                    <svg
                      aria-hidden="true"
                      class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-i4bv87-MuiSvgIcon-root"
                      data-testid="ChevronLeftIcon"
                      focusable="false"
                      viewBox="0 0 24 24"
                    >
                      <path
                        d="M15.41 7.41 14 6l-6 6 6 6 1.41-1.41L10.83 12z"
                      />
                    </svg>
                  </button>
                </span>
                <span
                  aria-label="Go to next page"
                  class=""
                  data-mui-internal-clone-element="true"
                >
                  <button
                    aria-label="Go to next page"
                    class="MuiButtonBase-root MuiIconButton-root MuiIconButton-sizeSmall css-hvz71z-MuiButtonBase-root-MuiIconButton-root"
                    tabindex="0"
                    type="button"
                  >
                    <svg
                      aria-hidden="true"
                      class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-i4bv87-MuiSvgIcon-root"
                      data-testid="ChevronRightIcon"
                      focusable="false"
                      viewBox="0 0 24 24"
                    >
                      <path
                        d="M10 6 8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z"
                      />
                    </svg>
                    <span
                      class="MuiTouchRipple-root css-8je8zh-MuiTouchRipple-root"
                    />
                  </button>
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</body>