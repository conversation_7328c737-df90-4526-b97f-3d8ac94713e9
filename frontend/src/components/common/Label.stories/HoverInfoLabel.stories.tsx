/*
 * Copyright 2025 The Kubernetes Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { Meta, StoryFn } from '@storybook/react';
import { HoverInfoLabel as HoverInfoLabelComponent, HoverInfoLabelProps } from '../Label';

export default {
  title: 'Label/HoverInfoLabel',
  component: HoverInfoLabelComponent,
  argTypes: {},
} as Meta;

const Template: StoryFn<HoverInfoLabelProps> = args => <HoverInfoLabelComponent {...args} />;

export const HoverInfoLabel = Template.bind({});
HoverInfoLabel.args = {
  label: 'Some label',
  hoverInfo: 'hover info',
};

export const HoverInfoLabelInfo = Template.bind({});
HoverInfoLabelInfo.args = {
  label: 'Some label',
  hoverInfo: <div>hover info div</div>,
};

export const LabelProps = Template.bind({});
LabelProps.args = {
  label: 'Some label',
  hoverInfo: <div>hover info div</div>,
  labelProps: {
    variant: 'body2',
  },
};

export const IconPosition = Template.bind({});
IconPosition.args = {
  label: 'Some label',
  hoverInfo: <div>hover info div</div>,
  iconPosition: 'start',
};

// icon isn't used in the codebase.
// export const HoverInfoLabelIcon = Template.bind({});
// HoverInfoLabelIcon.args = {
//   label: "Some label",
//   hoverInfo: "value",
//   icon: null, // unused it seems.
// };
