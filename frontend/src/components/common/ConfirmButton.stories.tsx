/*
 * Copyright 2025 The Kubernetes Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { Meta, StoryFn } from '@storybook/react';
import React from 'react';
import ConfirmButton, { ConfirmButtonProps } from './ConfirmButton';

export default {
  title: 'ConfirmButton',
  component: ConfirmButton,
  argTypes: {
    onConfirm: { action: 'confirmed' },
  },
} as Meta;

const Template: StoryFn<ConfirmButtonProps> = args => (
  <ConfirmButton {...args}>Confirm button</ConfirmButton>
);

export const Confirm = Template.bind({});
Confirm.args = {
  ariaLabel: 'confirm undo',
  confirmTitle: 'Are you sure?',
  confirmDescription: 'This will undo. Do you want to proceed?',
};

export const ExtendsButton = Template.bind({});
ExtendsButton.args = {
  color: 'secondary',
  ariaLabel: 'confirm undo',
  confirmTitle: 'Are you sure?',
  confirmDescription: 'This will undo. Do you want to proceed?',
};
