/*
 * Copyright 2025 The Kubernetes Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { Meta, StoryFn } from '@storybook/react';
import Pod from '../../../lib/k8s/pod';
import { TestContext } from '../../../test';
import { generateK8sResourceList } from '../../../test/mocker';
import { podList } from '../../pod/storyHelper';
import ResourceListView from './ResourceListView';

const phonyPods = generateK8sResourceList(podList[0], { instantiateAs: Pod });

export default {
  title: 'Resource/ListView',
  component: ResourceListView,
  argTypes: {},
  decorators: [
    Story => {
      return (
        <TestContext>
          <Story />
        </TestContext>
      );
    },
  ],
} as Meta;

const Template: StoryFn = () => {
  return (
    <ResourceListView
      title="My Pod List"
      data={phonyPods}
      columns={[
        'name',
        'namespace',
        {
          label: 'Num Containers',
          getValue: item => item?.spec.containers.length,
          show: false,
        },
        'age',
      ]}
    />
  );
};

export const OneHiddenColumn = Template.bind({});
