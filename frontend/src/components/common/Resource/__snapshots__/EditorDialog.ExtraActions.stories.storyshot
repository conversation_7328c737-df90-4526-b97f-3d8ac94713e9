<body>
  <div
    aria-hidden="true"
  />
  <div
    aria-busy="false"
    class="MuiDialog-root MuiModal-root css-zw3mfo-MuiModal-root-MuiDialog-root"
    role="presentation"
  >
    <div
      aria-hidden="true"
      class="MuiBackdrop-root MuiModal-backdrop css-yiavyu-MuiBackdrop-root-MuiDialog-backdrop"
      style="opacity: 1; webkit-transition: opacity 225ms cubic-bezier(0.4, 0, 0.2, 1) 0ms; transition: opacity 225ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;"
    />
    <div
      data-testid="sentinelStart"
      tabindex="0"
    />
    <div
      class="MuiDialog-container MuiDialog-scrollPaper css-hz1bth-MuiDialog-container"
      role="presentation"
      style="opacity: 1; webkit-transition: opacity 225ms cubic-bezier(0.4, 0, 0.2, 1) 0ms; transition: opacity 225ms cubic-bezier(0.4, 0, 0.2, 1) 0ms;"
      tabindex="-1"
    >
      <div
        aria-labelledby="editor-dialog-title-id"
        class="MuiPaper-root MuiPaper-outlined MuiPaper-rounded MuiDialog-paper MuiDialog-paperScrollPaper MuiDialog-paperWidthLg MuiDialog-paperFullWidth css-kl791l-MuiPaper-root-MuiDialog-paper"
        role="dialog"
      >
        <h2
          class="MuiTypography-root MuiTypography-h6 MuiDialogTitle-root css-8yphvn-MuiTypography-root-MuiDialogTitle-root"
          id="editor-dialog-title-id"
          style="display: flex;"
        >
          <div
            class="MuiGrid-root MuiGrid-container css-9cyib4-MuiGrid-root"
          >
            <div
              class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
            >
              <h1
                class="MuiTypography-root MuiTypography-h1 css-1kazmbo-MuiTypography-root"
                style="font-size: 1.25rem; font-weight: 500; line-height: 1.6;"
              >
                Edit: Dummy_Name
              </h1>
            </div>
            <div
              class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
            >
              <div
                class="MuiBox-root css-0"
              >
                <button
                  aria-label="Toggle fullscreen"
                  class="MuiButtonBase-root MuiIconButton-root MuiIconButton-sizeMedium css-whz9ym-MuiButtonBase-root-MuiIconButton-root"
                  data-mui-internal-clone-element="true"
                  tabindex="0"
                  type="button"
                >
                  <span
                    class="MuiTouchRipple-root css-8je8zh-MuiTouchRipple-root"
                  />
                </button>
                <button
                  aria-label="Close"
                  class="MuiButtonBase-root MuiIconButton-root MuiIconButton-sizeMedium css-whz9ym-MuiButtonBase-root-MuiIconButton-root"
                  data-mui-internal-clone-element="true"
                  tabindex="0"
                  type="button"
                >
                  <span
                    class="MuiTouchRipple-root css-8je8zh-MuiTouchRipple-root"
                  />
                </button>
              </div>
            </div>
          </div>
        </h2>
        <div
          class="MuiDialogContent-root css-erhy6c-MuiDialogContent-root"
        >
          <div
            class="MuiBox-root css-1jpm9pd"
          >
            <div
              class="MuiGrid-root MuiGrid-container MuiGrid-spacing-xs-2 css-1xu1mzw-MuiGrid-root"
            >
              <div
                class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
              >
                <div
                  class="MuiFormGroup-root MuiFormGroup-row css-qfz70r-MuiFormGroup-root"
                >
                  <label
                    class="MuiFormControlLabel-root MuiFormControlLabel-labelPlacementEnd css-j204z7-MuiFormControlLabel-root"
                  >
                    <span
                      class="MuiSwitch-root MuiSwitch-sizeMedium css-julti5-MuiSwitch-root"
                    >
                      <span
                        class="MuiButtonBase-root MuiSwitch-switchBase MuiSwitch-colorPrimary Mui-checked PrivateSwitchBase-root MuiSwitch-switchBase MuiSwitch-colorPrimary Mui-checked Mui-checked css-1emuodk-MuiButtonBase-root-MuiSwitch-switchBase"
                      >
                        <input
                          checked=""
                          class="PrivateSwitchBase-input MuiSwitch-input css-1m9pwf3"
                          type="checkbox"
                        />
                        <span
                          class="MuiSwitch-thumb css-jsexje-MuiSwitch-thumb"
                        />
                        <span
                          class="MuiTouchRipple-root css-8je8zh-MuiTouchRipple-root"
                        />
                      </span>
                      <span
                        class="MuiSwitch-track css-1yjjitx-MuiSwitch-track"
                      />
                    </span>
                    <span
                      class="MuiTypography-root MuiTypography-body1 MuiFormControlLabel-label css-1ezega9-MuiTypography-root"
                    >
                      Extra Action Switch
                    </span>
                  </label>
                </div>
              </div>
              <div
                class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
              >
                <div
                  class="MuiFormGroup-root MuiFormGroup-row css-qfz70r-MuiFormGroup-root"
                >
                  <label
                    class="MuiFormControlLabel-root MuiFormControlLabel-labelPlacementEnd css-j204z7-MuiFormControlLabel-root"
                  >
                    <span
                      class="MuiSwitch-root MuiSwitch-sizeMedium css-julti5-MuiSwitch-root"
                    >
                      <span
                        class="MuiButtonBase-root MuiSwitch-switchBase MuiSwitch-colorPrimary PrivateSwitchBase-root MuiSwitch-switchBase MuiSwitch-colorPrimary css-1emuodk-MuiButtonBase-root-MuiSwitch-switchBase"
                      >
                        <input
                          class="PrivateSwitchBase-input MuiSwitch-input css-1m9pwf3"
                          name="useSimpleEditor"
                          type="checkbox"
                        />
                        <span
                          class="MuiSwitch-thumb css-jsexje-MuiSwitch-thumb"
                        />
                        <span
                          class="MuiTouchRipple-root css-8je8zh-MuiTouchRipple-root"
                        />
                      </span>
                      <span
                        class="MuiSwitch-track css-1yjjitx-MuiSwitch-track"
                      />
                    </span>
                    <span
                      class="MuiTypography-root MuiTypography-body1 MuiFormControlLabel-label css-1ezega9-MuiTypography-root"
                    >
                      Use minimal editor
                    </span>
                  </label>
                </div>
              </div>
            </div>
          </div>
          <div
            class="MuiTabs-root css-1ujnqem-MuiTabs-root"
          >
            <div
              class="MuiTabs-scrollableX MuiTabs-hideScrollbar css-oqr85h"
              style="width: 99px; height: 99px; position: absolute; top: -9999px; overflow: scroll;"
            />
            <div
              class="MuiTabs-scroller MuiTabs-hideScrollbar MuiTabs-scrollableX css-69z67c-MuiTabs-scroller"
              style="margin-bottom: 0px;"
            >
              <div
                aria-label="Editor"
                class="MuiTabs-flexContainer css-heg063-MuiTabs-flexContainer"
                role="tablist"
              >
                <button
                  aria-controls="full-width-tabpanel-0-Editor-tabs-id"
                  aria-selected="true"
                  class="MuiButtonBase-root MuiTab-root MuiTab-textColorPrimary Mui-selected css-f00lt7-MuiButtonBase-root-MuiTab-root"
                  id="full-width-tab-0-Editor-tabs-id"
                  role="tab"
                  tabindex="0"
                  type="button"
                >
                  Editor
                  <span
                    class="MuiTouchRipple-root css-8je8zh-MuiTouchRipple-root"
                  />
                </button>
                <button
                  aria-controls="full-width-tabpanel-1-Editor-tabs-id"
                  aria-selected="false"
                  class="MuiButtonBase-root MuiTab-root MuiTab-textColorPrimary css-f00lt7-MuiButtonBase-root-MuiTab-root"
                  id="full-width-tab-1-Editor-tabs-id"
                  role="tab"
                  tabindex="-1"
                  type="button"
                >
                  Documentation
                  <span
                    class="MuiTouchRipple-root css-8je8zh-MuiTouchRipple-root"
                  />
                </button>
              </div>
              <span
                class="MuiTabs-indicator css-1s2zz1g-MuiTabs-indicator"
                style="left: 0px; width: 0px;"
              />
            </div>
          </div>
          <div
            aria-labelledby="full-width-tab-0-Editor-tabs-id"
            class="MuiTypography-root MuiTypography-body1 css-1ezega9-MuiTypography-root"
            id="full-width-tabpanel-0-Editor-tabs-id"
            role="tabpanel"
          >
            <div
              class="MuiBox-root css-10klw3m"
            >
              <div
                class="mock-monaco-editor"
              />
            </div>
          </div>
          <div
            aria-labelledby="full-width-tab-1-Editor-tabs-id"
            class="MuiTypography-root MuiTypography-body1 css-1ezega9-MuiTypography-root"
            hidden=""
            id="full-width-tabpanel-1-Editor-tabs-id"
            role="tabpanel"
          >
            <div
              class="MuiBox-root css-94bk7g"
            >
              <div
                class="MuiBox-root css-19midj6"
              >
                <p
                  class="MuiTypography-root MuiTypography-body1 MuiTypography-alignCenter css-18lkse1-MuiTypography-root"
                >
                  No documentation available.
                </p>
              </div>
            </div>
          </div>
        </div>
        <div
          class="MuiDialogActions-root MuiDialogActions-spacing css-knqc4i-MuiDialogActions-root"
        >
          <button
            aria-label="Undo"
            class="MuiButtonBase-root MuiButton-root MuiButton-contained MuiButton-containedSecondary MuiButton-sizeMedium MuiButton-containedSizeMedium MuiButton-colorSecondary MuiButton-disableElevation Mui-disabled MuiButton-root MuiButton-contained MuiButton-containedSecondary MuiButton-sizeMedium MuiButton-containedSizeMedium MuiButton-colorSecondary MuiButton-disableElevation css-14f6w6s-MuiButtonBase-root-MuiButton-root"
            disabled=""
            tabindex="-1"
            type="button"
          >
            Undo Changes
          </button>
          <div />
          <div
            style="flex: 1 0 0px;"
          />
          <div
            style="flex: 1 0 0px;"
          />
          <button
            class="MuiButtonBase-root MuiButton-root MuiButton-contained MuiButton-containedSecondary MuiButton-sizeMedium MuiButton-containedSizeMedium MuiButton-colorSecondary MuiButton-disableElevation MuiButton-root MuiButton-contained MuiButton-containedSecondary MuiButton-sizeMedium MuiButton-containedSizeMedium MuiButton-colorSecondary MuiButton-disableElevation css-14f6w6s-MuiButtonBase-root-MuiButton-root"
            tabindex="0"
            type="button"
          >
            Close
            <span
              class="MuiTouchRipple-root css-8je8zh-MuiTouchRipple-root"
            />
          </button>
          <button
            class="MuiButtonBase-root MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeMedium MuiButton-containedSizeMedium MuiButton-colorPrimary MuiButton-disableElevation Mui-disabled MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeMedium MuiButton-containedSizeMedium MuiButton-colorPrimary MuiButton-disableElevation css-gn8fa3-MuiButtonBase-root-MuiButton-root"
            disabled=""
            tabindex="-1"
            type="button"
          >
            Save & Apply
          </button>
        </div>
      </div>
    </div>
    <div
      data-testid="sentinelEnd"
      tabindex="0"
    />
  </div>
</body>