/*
 * Copyright 2025 The Kubernetes Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { Meta, StoryFn } from '@storybook/react';
import { TestContext } from '../../../test';
import TimezoneSelect from './TimezoneSelect';

export default {
  title: 'TimezoneSelect',
  component: TimezoneSelect,
  decorators: [
    Story => (
      <TestContext>
        <Story />
      </TestContext>
    ),
  ],
} as Meta;

const Template: StoryFn<typeof TimezoneSelect> = args => <TimezoneSelect {...args} />;

export const Default = Template.bind({});
Default.args = {
  initialTimezone: 'Etc/Utc',
  onChange: (timezone: string) => {
    console.log(`Timezone changed to: ${timezone}`);
  },
};

export const NoInitialValue = Template.bind({});
NoInitialValue.args = {
  onChange: (timezone: string) => {
    console.log('Timezone changed:', timezone);
  },
};
