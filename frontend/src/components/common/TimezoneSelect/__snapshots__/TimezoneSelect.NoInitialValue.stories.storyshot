<body>
  <div>
    <div
      class="MuiAutocomplete-root MuiAutocomplete-hasPopupIcon css-ka7ti6-MuiAutocomplete-root"
    >
      <div
        class="MuiFormControl-root MuiForm<PERSON>ontrol-fullWidth MuiTextField-root css-wb57ya-MuiFormControl-root-MuiTextField-root"
      >
        <div
          class="MuiInputBase-root MuiOutlinedInput-root MuiInputBase-colorPrimary MuiInputBase-fullWidth MuiInputBase-formControl MuiInputBase-sizeSmall MuiInputBase-adornedEnd MuiAutocomplete-inputRoot css-1xjtaff-MuiInputBase-root-MuiOutlinedInput-root"
        >
          <input
            aria-autocomplete="both"
            aria-describedby="cluster-selector-autocomplete-helper-text"
            aria-expanded="false"
            aria-invalid="false"
            autocapitalize="none"
            autocomplete="off"
            class="MuiInputBase-input MuiOutlinedInput-input MuiInputBase-inputSizeSmall MuiInputBase-inputAdornedEnd MuiAutocomplete-input MuiAutocomplete-inputFocused css-19qh8xo-MuiInputBase-input-MuiOutlinedInput-input"
            id="cluster-selector-autocomplete"
            role="combobox"
            spellcheck="false"
            type="text"
            value=""
          />
          <div
            class="MuiAutocomplete-endAdornment css-p1olib-MuiAutocomplete-endAdornment"
          >
            <button
              aria-label="Open"
              class="MuiButtonBase-root MuiIconButton-root MuiIconButton-sizeMedium MuiAutocomplete-popupIndicator css-1aav1nn-MuiButtonBase-root-MuiIconButton-root-MuiAutocomplete-popupIndicator"
              tabindex="-1"
              title="Open"
              type="button"
            >
              <svg
                aria-hidden="true"
                class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-i4bv87-MuiSvgIcon-root"
                data-testid="ArrowDropDownIcon"
                focusable="false"
                viewBox="0 0 24 24"
              >
                <path
                  d="M7 10l5 5 5-5z"
                />
              </svg>
              <span
                class="MuiTouchRipple-root css-8je8zh-MuiTouchRipple-root"
              />
            </button>
          </div>
          <fieldset
            aria-hidden="true"
            class="MuiOutlinedInput-notchedOutline css-1d3z3hw-MuiOutlinedInput-notchedOutline"
          >
            <legend
              class="css-ihdtdm"
            >
              <span
                class="notranslate"
              >
                ​
              </span>
            </legend>
          </fieldset>
        </div>
        <p
          class="MuiFormHelperText-root MuiFormHelperText-sizeSmall MuiFormHelperText-contained css-153yz37-MuiFormHelperText-root"
          id="cluster-selector-autocomplete-helper-text"
        >
          Timezone
        </p>
      </div>
    </div>
  </div>
</body>