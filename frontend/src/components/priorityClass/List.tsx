/*
 * Copyright 2025 The Kubernetes Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { useTranslation } from 'react-i18next';
import PriorityClass from '../../lib/k8s/priorityClass';
import ResourceListView from '../common/Resource/ResourceListView';

export default function PriorityClassList() {
  const { t } = useTranslation(['glossary']);

  return (
    <ResourceListView
      title={t('glossary|PriorityClass')}
      resourceClass={PriorityClass}
      columns={[
        'name',
        'cluster',
        {
          id: 'value',
          label: t('translation|Value'),
          gridTemplate: 'min-content',
          getValue: item => item.value,
        },
        {
          id: 'globalDefault',
          label: t('translation|Global Default'),
          gridTemplate: 'min-content',
          getValue: item => String(item.globalDefault || 'False'),
        },
        'age',
      ]}
    />
  );
}
