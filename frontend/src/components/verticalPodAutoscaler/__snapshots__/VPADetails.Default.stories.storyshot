<body>
  <div>
    <div
      class="MuiGrid-root MuiGrid-container MuiGrid-spacing-xs-1 css-bsyb48-MuiGrid-root"
    >
      <div
        class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 css-49904w-MuiGrid-root"
      >
        <div
          class="MuiBox-root css-p0cik4"
        >
          <button
            class="MuiButtonBase-root MuiButton-root MuiButton-text Mu<PERSON><PERSON><PERSON>on-textPrimary MuiButton-sizeSmall MuiButton-textSizeSmall MuiButton-colorPrimary MuiButton-disableElevation MuiButton-root MuiButton-text MuiButton-textPrimary MuiButton-sizeSmall MuiButton-textSizeSmall MuiButton-colorPrimary MuiButton-disableElevation css-1j11y9k-MuiButtonBase-root-MuiButton-root"
            tabindex="0"
            type="button"
          >
            <span
              class="MuiButton-icon MuiButton-startIcon MuiButton-iconSizeSmall css-y6rp3m-MuiButton-startIcon"
            />
            <p
              class="MuiTypography-root MuiTypography-body1 css-1ezega9-MuiTypography-root"
              style="padding-top: 3px;"
            >
              Back
            </p>
            <span
              class="MuiTouchRipple-root css-8je8zh-MuiTouchRipple-root"
            />
          </button>
        </div>
      </div>
      <div
        class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 css-49904w-MuiGrid-root"
      >
        <div
          class="MuiBox-root css-p0cik4"
        >
          <div
            class="MuiGrid-root MuiGrid-container MuiGrid-spacing-xs-2 css-1ts0dnm-MuiGrid-root"
          >
            <div
              class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
            >
              <div
                class="MuiBox-root css-70qvj9"
              >
                <h1
                  class="MuiTypography-root MuiTypography-h1 MuiTypography-noWrap css-yeaech-MuiTypography-root"
                >
                  VerticalPodAutoscaler: multi-container-vpa
                </h1>
                <div
                  class="MuiBox-root css-ldp2l3"
                />
              </div>
            </div>
            <div
              class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
            >
              <div
                class="MuiGrid-root MuiGrid-container MuiGrid-item css-ztq4zc-MuiGrid-root"
              >
                <div
                  class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                />
                <div
                  class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                />
                <div
                  class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                >
                  <button
                    aria-label="Edit"
                    class="MuiButtonBase-root MuiIconButton-root MuiIconButton-sizeMedium css-whz9ym-MuiButtonBase-root-MuiIconButton-root"
                    data-mui-internal-clone-element="true"
                    tabindex="0"
                    type="button"
                  >
                    <span
                      class="MuiTouchRipple-root css-8je8zh-MuiTouchRipple-root"
                    />
                  </button>
                </div>
                <div
                  class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
                >
                  <button
                    aria-label="Delete"
                    class="MuiButtonBase-root MuiIconButton-root MuiIconButton-sizeMedium css-whz9ym-MuiButtonBase-root-MuiIconButton-root"
                    data-mui-internal-clone-element="true"
                    tabindex="0"
                    type="button"
                  >
                    <span
                      class="MuiTouchRipple-root css-8je8zh-MuiTouchRipple-root"
                    />
                  </button>
                  <div />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 css-49904w-MuiGrid-root"
      >
        <div
          class="MuiBox-root css-p0cik4"
        >
          <div
            class="MuiBox-root css-j1fy4m"
          >
            <div
              aria-busy="false"
              aria-live="polite"
              class="MuiBox-root css-1txv3mw"
            >
              <div
                class="MuiBox-root css-0"
              >
                <dl
                  class="MuiGrid-root MuiGrid-container css-kxuems-MuiGrid-root"
                >
                  <dt
                    class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-4 css-1iczkge-MuiGrid-root"
                  >
                    Name
                  </dt>
                  <dd
                    class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-8 css-deb4a-MuiGrid-root"
                  >
                    <span
                      class="MuiTypography-root MuiTypography-body1 css-e06lsu-MuiTypography-root"
                    >
                      multi-container-vpa
                    </span>
                  </dd>
                  <dt
                    class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-4 css-1iczkge-MuiGrid-root"
                  >
                    Namespace
                  </dt>
                  <dd
                    class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-8 css-deb4a-MuiGrid-root"
                  >
                    <a
                      class="MuiTypography-root MuiTypography-inherit MuiLink-root MuiLink-underlineHover css-1w0w5yv-MuiTypography-root-MuiLink-root"
                      href="/"
                    >
                      default
                    </a>
                  </dd>
                  <dt
                    class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-4 css-1iczkge-MuiGrid-root"
                  >
                    Creation
                  </dt>
                  <dd
                    class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-8 css-deb4a-MuiGrid-root"
                  >
                    <span
                      class="MuiTypography-root MuiTypography-body1 css-e06lsu-MuiTypography-root"
                    >
                      2023-11-23T07:18:45.000Z
                    </span>
                  </dd>
                  <dt
                    class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-4 css-1iczkge-MuiGrid-root"
                  >
                    Annotations
                  </dt>
                  <dd
                    class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-8 css-deb4a-MuiGrid-root"
                  >
                    <div
                      class="MuiBox-root css-yi3mkw"
                    >
                      <p
                        aria-label="kubectl.kubernetes.io/last-applied-configuration: {"apiVersion":"autoscaling.k8s.io/v1","kind":"VerticalPodAutoscaler","metadata":{"annotations":{},"name":"multi-container-vpa","namespace":"default"},"spec":{"resourcePolicy":{"containerPolicies":[{"containerName":"web-container","controlledResources":["cpu","memory"],"controlledValues":"RequestsAndLimits","minAllowed":{"cpu":"80m","memory":"512Mi"}},{"containerName":"db-container","controlledResources":["cpu","memory"],"controlledValues":"RequestsAndLimits","minAllowed":{"cpu":"1000m","memory":"2Gi"}}]},"targetRef":{"apiVersion":"apps/v1","kind":"Deployment","name":"multi-container-deployment"},"updatePolicy":{"updateMode":"Auto"}}}
"
                        class="MuiTypography-root MuiTypography-body1 css-1on669h-MuiTypography-root"
                        data-mui-internal-clone-element="true"
                      >
                        kubectl.kubernetes.io/last-applied-configuration: …
                      </p>
                    </div>
                  </dd>
                  <dt
                    class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-4 css-1iczkge-MuiGrid-root"
                  >
                    Reference
                  </dt>
                  <dd
                    class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-8 css-deb4a-MuiGrid-root"
                  >
                    <a
                      class="MuiTypography-root MuiTypography-inherit MuiLink-root MuiLink-underlineHover css-1w0w5yv-MuiTypography-root-MuiLink-root"
                      href="/"
                    >
                      Deployment
                      /
                      multi-container-deployment
                    </a>
                  </dd>
                  <dt
                    class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-4 css-iqixpy-MuiGrid-root"
                  >
                    Update Policy
                  </dt>
                  <dd
                    class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 MuiGrid-grid-sm-8 css-1xrovmc-MuiGrid-root"
                  >
                    Update Mode
                    :
                    Auto
                  </dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 css-49904w-MuiGrid-root"
      >
        <div
          class="MuiBox-root css-p0cik4"
        >
          <div
            class="MuiBox-root css-j1fy4m"
          >
            <div
              class="MuiGrid-root MuiGrid-container MuiGrid-spacing-xs-2 css-1ts0dnm-MuiGrid-root"
            >
              <div
                class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
              >
                <div
                  class="MuiBox-root css-70qvj9"
                >
                  <h2
                    class="MuiTypography-root MuiTypography-h2 MuiTypography-noWrap css-m5vcfd-MuiTypography-root"
                  >
                    Container Policy
                  </h2>
                  <div
                    class="MuiBox-root css-ldp2l3"
                  />
                </div>
              </div>
            </div>
            <div
              class="MuiBox-root css-1txv3mw"
            >
              <div
                class="MuiPaper-root MuiPaper-outlined MuiPaper-rounded MuiTableContainer-root css-onzayo-MuiPaper-root-MuiTableContainer-root"
              >
                <table
                  class="MuiTable-root css-1t02l4h-MuiTable-root"
                >
                  <thead
                    class="MuiTableHead-root css-15wwp11-MuiTableHead-root"
                  >
                    <tr
                      class="MuiTableRow-root MuiTableRow-head css-13jktim-MuiTableRow-root"
                    >
                      <th
                        class="MuiTableCell-root MuiTableCell-head MuiTableCell-sizeSmall css-l4jzay-MuiTableCell-root"
                        scope="col"
                      >
                        Container
                      </th>
                      <th
                        class="MuiTableCell-root MuiTableCell-head MuiTableCell-sizeSmall css-l4jzay-MuiTableCell-root"
                        scope="col"
                      >
                        Controlled Resources
                      </th>
                      <th
                        class="MuiTableCell-root MuiTableCell-head MuiTableCell-sizeSmall css-l4jzay-MuiTableCell-root"
                        scope="col"
                      >
                        Controlled Values
                      </th>
                      <th
                        class="MuiTableCell-root MuiTableCell-head MuiTableCell-sizeSmall css-l4jzay-MuiTableCell-root"
                        scope="col"
                      >
                        Min Allowed
                      </th>
                      <th
                        class="MuiTableCell-root MuiTableCell-head MuiTableCell-sizeSmall css-l4jzay-MuiTableCell-root"
                        scope="col"
                      >
                        Max Allowed
                      </th>
                      <th
                        class="MuiTableCell-root MuiTableCell-head MuiTableCell-sizeSmall css-l4jzay-MuiTableCell-root"
                        scope="col"
                      >
                        Mode
                      </th>
                    </tr>
                  </thead>
                  <tbody
                    class="MuiTableBody-root css-apqrd9-MuiTableBody-root"
                  >
                    <tr
                      class="MuiTableRow-root css-13jktim-MuiTableRow-root"
                    >
                      <td
                        class="MuiTableCell-root MuiTableCell-body MuiTableCell-sizeSmall css-oh2q4q-MuiTableCell-root"
                      >
                        web-container
                      </td>
                      <td
                        class="MuiTableCell-root MuiTableCell-body MuiTableCell-sizeSmall css-oh2q4q-MuiTableCell-root"
                      >
                        cpu, memory
                      </td>
                      <td
                        class="MuiTableCell-root MuiTableCell-body MuiTableCell-sizeSmall css-oh2q4q-MuiTableCell-root"
                      >
                        RequestsAndLimits
                      </td>
                      <td
                        class="MuiTableCell-root MuiTableCell-body MuiTableCell-sizeSmall css-oh2q4q-MuiTableCell-root"
                      >
                        80m,512Mi
                      </td>
                      <td
                        class="MuiTableCell-root MuiTableCell-body MuiTableCell-sizeSmall css-oh2q4q-MuiTableCell-root"
                      />
                      <td
                        class="MuiTableCell-root MuiTableCell-body MuiTableCell-sizeSmall css-oh2q4q-MuiTableCell-root"
                      />
                    </tr>
                    <tr
                      class="MuiTableRow-root css-13jktim-MuiTableRow-root"
                    >
                      <td
                        class="MuiTableCell-root MuiTableCell-body MuiTableCell-sizeSmall css-oh2q4q-MuiTableCell-root"
                      >
                        db-container
                      </td>
                      <td
                        class="MuiTableCell-root MuiTableCell-body MuiTableCell-sizeSmall css-oh2q4q-MuiTableCell-root"
                      >
                        cpu, memory
                      </td>
                      <td
                        class="MuiTableCell-root MuiTableCell-body MuiTableCell-sizeSmall css-oh2q4q-MuiTableCell-root"
                      >
                        RequestsAndLimits
                      </td>
                      <td
                        class="MuiTableCell-root MuiTableCell-body MuiTableCell-sizeSmall css-oh2q4q-MuiTableCell-root"
                      >
                        1000m,2Gi
                      </td>
                      <td
                        class="MuiTableCell-root MuiTableCell-body MuiTableCell-sizeSmall css-oh2q4q-MuiTableCell-root"
                      />
                      <td
                        class="MuiTableCell-root MuiTableCell-body MuiTableCell-sizeSmall css-oh2q4q-MuiTableCell-root"
                      />
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 css-49904w-MuiGrid-root"
      >
        <div
          class="MuiBox-root css-p0cik4"
        >
          <div
            class="MuiBox-root css-j1fy4m"
          >
            <div
              class="MuiGrid-root MuiGrid-container MuiGrid-spacing-xs-2 css-1ts0dnm-MuiGrid-root"
            >
              <div
                class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
              >
                <div
                  class="MuiBox-root css-70qvj9"
                >
                  <h2
                    class="MuiTypography-root MuiTypography-h2 MuiTypography-noWrap css-m5vcfd-MuiTypography-root"
                  >
                    Conditions
                  </h2>
                  <div
                    class="MuiBox-root css-ldp2l3"
                  />
                </div>
              </div>
            </div>
            <div
              class="MuiBox-root css-1txv3mw"
            >
              <div
                class="MuiPaper-root MuiPaper-outlined MuiPaper-rounded MuiTableContainer-root css-onzayo-MuiPaper-root-MuiTableContainer-root"
              >
                <table
                  class="MuiTable-root css-r7i92b-MuiTable-root"
                >
                  <thead
                    class="MuiTableHead-root css-15wwp11-MuiTableHead-root"
                  >
                    <tr
                      class="MuiTableRow-root MuiTableRow-head css-13jktim-MuiTableRow-root"
                    >
                      <th
                        class="MuiTableCell-root MuiTableCell-head MuiTableCell-sizeSmall css-l4jzay-MuiTableCell-root"
                        scope="col"
                      >
                        Type
                      </th>
                      <th
                        class="MuiTableCell-root MuiTableCell-head MuiTableCell-sizeSmall css-l4jzay-MuiTableCell-root"
                        scope="col"
                      >
                        Status
                      </th>
                      <th
                        class="MuiTableCell-root MuiTableCell-head MuiTableCell-sizeSmall css-l4jzay-MuiTableCell-root"
                        scope="col"
                      >
                        Reason
                      </th>
                      <th
                        class="MuiTableCell-root MuiTableCell-head MuiTableCell-sizeSmall css-l4jzay-MuiTableCell-root"
                        scope="col"
                      >
                        Message
                      </th>
                      <th
                        class="MuiTableCell-root MuiTableCell-head MuiTableCell-sizeSmall css-l4jzay-MuiTableCell-root"
                        scope="col"
                      >
                        Last Transition Time
                      </th>
                    </tr>
                  </thead>
                  <tbody
                    class="MuiTableBody-root css-apqrd9-MuiTableBody-root"
                  >
                    <tr
                      class="MuiTableRow-root css-13jktim-MuiTableRow-root"
                    >
                      <td
                        class="MuiTableCell-root MuiTableCell-body MuiTableCell-sizeSmall css-oh2q4q-MuiTableCell-root"
                      >
                        RecommendationProvided
                      </td>
                      <td
                        class="MuiTableCell-root MuiTableCell-body MuiTableCell-sizeSmall css-oh2q4q-MuiTableCell-root"
                      >
                        True
                      </td>
                      <td
                        class="MuiTableCell-root MuiTableCell-body MuiTableCell-sizeSmall css-oh2q4q-MuiTableCell-root"
                      />
                      <td
                        class="MuiTableCell-root MuiTableCell-body MuiTableCell-sizeSmall css-oh2q4q-MuiTableCell-root"
                      />
                      <td
                        class="MuiTableCell-root MuiTableCell-body MuiTableCell-sizeSmall css-oh2q4q-MuiTableCell-root"
                      >
                        <p
                          aria-label="2023-11-23T07:18:48.000Z"
                          class="MuiTypography-root MuiTypography-body1 css-1d0cpfm-MuiTypography-root"
                          data-mui-internal-clone-element="true"
                        >
                          3 months
                        </p>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 css-49904w-MuiGrid-root"
      >
        <div
          class="MuiBox-root css-p0cik4"
        >
          <div
            class="MuiBox-root css-j1fy4m"
          >
            <div
              class="MuiGrid-root MuiGrid-container MuiGrid-spacing-xs-2 css-1ts0dnm-MuiGrid-root"
            >
              <div
                class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
              >
                <div
                  class="MuiBox-root css-70qvj9"
                >
                  <h2
                    class="MuiTypography-root MuiTypography-h2 MuiTypography-noWrap css-m5vcfd-MuiTypography-root"
                  >
                    Recommendations
                  </h2>
                  <div
                    class="MuiBox-root css-ldp2l3"
                  />
                </div>
              </div>
            </div>
            <div
              class="MuiBox-root css-1txv3mw"
            >
              <div
                class="MuiPaper-root MuiPaper-outlined MuiPaper-rounded MuiTableContainer-root css-onzayo-MuiPaper-root-MuiTableContainer-root"
              >
                <table
                  class="MuiTable-root css-r7i92b-MuiTable-root"
                >
                  <thead
                    class="MuiTableHead-root css-15wwp11-MuiTableHead-root"
                  >
                    <tr
                      class="MuiTableRow-root MuiTableRow-head css-13jktim-MuiTableRow-root"
                    >
                      <th
                        class="MuiTableCell-root MuiTableCell-head MuiTableCell-sizeSmall css-l4jzay-MuiTableCell-root"
                        scope="col"
                      >
                        Container
                      </th>
                      <th
                        class="MuiTableCell-root MuiTableCell-head MuiTableCell-sizeSmall css-l4jzay-MuiTableCell-root"
                        scope="col"
                      >
                        Lower Bound
                      </th>
                      <th
                        class="MuiTableCell-root MuiTableCell-head MuiTableCell-sizeSmall css-l4jzay-MuiTableCell-root"
                        scope="col"
                      >
                        Target
                      </th>
                      <th
                        class="MuiTableCell-root MuiTableCell-head MuiTableCell-sizeSmall css-l4jzay-MuiTableCell-root"
                        scope="col"
                      >
                        Upper Bound
                      </th>
                      <th
                        class="MuiTableCell-root MuiTableCell-head MuiTableCell-sizeSmall css-l4jzay-MuiTableCell-root"
                        scope="col"
                      >
                        Uncapped Target
                      </th>
                    </tr>
                  </thead>
                  <tbody
                    class="MuiTableBody-root css-apqrd9-MuiTableBody-root"
                  >
                    <tr
                      class="MuiTableRow-root css-13jktim-MuiTableRow-root"
                    >
                      <td
                        class="MuiTableCell-root MuiTableCell-body MuiTableCell-sizeSmall css-oh2q4q-MuiTableCell-root"
                      >
                        db-container
                      </td>
                      <td
                        class="MuiTableCell-root MuiTableCell-body MuiTableCell-sizeSmall css-oh2q4q-MuiTableCell-root"
                      >
                        cpu:1,memory:2Gi
                      </td>
                      <td
                        class="MuiTableCell-root MuiTableCell-body MuiTableCell-sizeSmall css-oh2q4q-MuiTableCell-root"
                      >
                        cpu:1,memory:2Gi
                      </td>
                      <td
                        class="MuiTableCell-root MuiTableCell-body MuiTableCell-sizeSmall css-oh2q4q-MuiTableCell-root"
                      >
                        cpu:1,memory:2Gi
                      </td>
                      <td
                        class="MuiTableCell-root MuiTableCell-body MuiTableCell-sizeSmall css-oh2q4q-MuiTableCell-root"
                      >
                        cpu:12m,memory:131072k
                      </td>
                    </tr>
                    <tr
                      class="MuiTableRow-root css-13jktim-MuiTableRow-root"
                    >
                      <td
                        class="MuiTableCell-root MuiTableCell-body MuiTableCell-sizeSmall css-oh2q4q-MuiTableCell-root"
                      >
                        web-container
                      </td>
                      <td
                        class="MuiTableCell-root MuiTableCell-body MuiTableCell-sizeSmall css-oh2q4q-MuiTableCell-root"
                      >
                        cpu:80m,memory:512Mi
                      </td>
                      <td
                        class="MuiTableCell-root MuiTableCell-body MuiTableCell-sizeSmall css-oh2q4q-MuiTableCell-root"
                      >
                        cpu:80m,memory:512Mi
                      </td>
                      <td
                        class="MuiTableCell-root MuiTableCell-body MuiTableCell-sizeSmall css-oh2q4q-MuiTableCell-root"
                      >
                        cpu:80m,memory:512Mi
                      </td>
                      <td
                        class="MuiTableCell-root MuiTableCell-body MuiTableCell-sizeSmall css-oh2q4q-MuiTableCell-root"
                      >
                        cpu:12m,memory:131072k
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        class="MuiGrid-root MuiGrid-item MuiGrid-grid-xs-12 css-49904w-MuiGrid-root"
      >
        <div
          class="MuiBox-root css-p0cik4"
        >
          <div
            class="MuiBox-root css-j1fy4m"
          >
            <div
              class="MuiGrid-root MuiGrid-container MuiGrid-spacing-xs-2 css-1ts0dnm-MuiGrid-root"
            >
              <div
                class="MuiGrid-root MuiGrid-item css-13i4rnv-MuiGrid-root"
              >
                <div
                  class="MuiBox-root css-70qvj9"
                >
                  <h2
                    class="MuiTypography-root MuiTypography-h2 MuiTypography-noWrap css-m5vcfd-MuiTypography-root"
                  >
                    Events
                  </h2>
                  <div
                    class="MuiBox-root css-ldp2l3"
                  />
                </div>
              </div>
            </div>
            <div
              class="MuiBox-root css-1txv3mw"
            >
              <div
                class="MuiPaper-root MuiPaper-outlined MuiPaper-rounded css-1guobrs-MuiPaper-root"
              >
                <div
                  class="MuiBox-root css-19midj6"
                >
                  <p
                    class="MuiTypography-root MuiTypography-body1 MuiTypography-alignCenter css-18lkse1-MuiTypography-root"
                  >
                    No data to be shown.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</body>