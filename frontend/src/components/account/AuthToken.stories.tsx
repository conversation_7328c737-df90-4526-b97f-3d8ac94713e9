/*
 * Copyright 2025 The Kubernetes Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { Meta, StoryFn } from '@storybook/react';
import { TestContext } from '../../test';
import { PureAuthToken, PureAuthTokenProps } from './Auth';

export default {
  title: 'AuthToken',
  component: PureAuthToken,
  argTypes: {
    onCancel: { action: 'cancel clicked' },
    onAuthClicked: { action: 'auth clicked' },
    onChangeToken: { action: 'token changed' },
    onCloseError: { action: 'error closed' },
  },
  decorators: [
    Story => {
      return (
        <TestContext>
          <Story />
        </TestContext>
      );
    },
  ],
} as Meta;

const Template: StoryFn<PureAuthTokenProps> = args => <PureAuthToken {...args} />;

export const ShowError = Template.bind({});
ShowError.args = {
  title: 'a title',
  token: 'a token',
  showError: true,
  showActions: false,
};

export const ShowActions = Template.bind({});
ShowActions.args = {
  title: 'a title',
  token: 'a token',
  showError: false,
  showActions: true,
};
