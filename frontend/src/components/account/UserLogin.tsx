/*
 * Copyright 2025 The Kubernetes Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import {
  Box,
  Button,
  DialogActions,
  DialogContent,
  DialogContentText,
  Snackbar,
  TextField,
} from '@mui/material';
import React from 'react';
import { Trans, useTranslation } from 'react-i18next';
import { generatePath, useHistory } from 'react-router-dom';
import { getCluster, getClusterPrefixedPath } from '../../lib/cluster';
import { useClustersConf } from '../../lib/k8s';
import { loginWithCredentials, getErrorMessage } from '../../lib/userAuth';
import { ClusterDialog } from '../cluster/Chooser';
import { DialogTitle } from '../common/Dialog';

export default function UserLogin() {
  const history = useHistory();
  const clusterConf = useClustersConf();
  const [username, setUsername] = React.useState('');
  const [password, setPassword] = React.useState('');
  const [showError, setShowError] = React.useState(false);
  const [errorMessage, setErrorMessage] = React.useState('');
  const [isLoading, setIsLoading] = React.useState(false);
  const clusters = useClustersConf();
  const { t } = useTranslation();

  async function onLoginClicked() {
    if (!username.trim() || !password.trim()) {
      setErrorMessage(t('Please enter both username and password'));
      setShowError(true);
      return;
    }

    setIsLoading(true);
    try {
      const code = await loginWithCredentials(username, password);
      if (code === 200) {
        // If successful, redirect.
        history.replace(
          generatePath(getClusterPrefixedPath(), {
            cluster: getCluster() as string,
          })
        );
      } else {
        setUsername('');
        setPassword('');
        setErrorMessage(getErrorMessage(code));
        setShowError(true);
      }
    } catch (error) {
      console.error('Login error:', error);
      setUsername('');
      setPassword('');
      setErrorMessage(t('Authentication failed. Please try again.'));
      setShowError(true);
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <PureUserLogin
      onCancel={() => history.replace('/')}
      title={
        Object.keys(clusterConf || {}).length > 1
          ? t('Authentication: {{ clusterName }}', { clusterName: getCluster() })
          : t('Authentication')
      }
      username={username}
      password={password}
      showError={showError}
      errorMessage={errorMessage}
      isLoading={isLoading}
      showActions={Object.keys(clusters || {}).length > 1}
      onChangeUsername={(event: React.ChangeEvent<HTMLInputElement>) =>
        setUsername(event.target.value)
      }
      onChangePassword={(event: React.ChangeEvent<HTMLInputElement>) =>
        setPassword(event.target.value)
      }
      onLoginClicked={onLoginClicked}
      onCloseError={() => {
        setShowError(false);
        setErrorMessage('');
      }}
    />
  );
}

interface ClickCallbackType {
  (event: React.MouseEvent<HTMLElement>): void;
}
interface ChangeCallbackType {
  (event: React.ChangeEvent<HTMLInputElement>): void;
}

export interface PureUserLoginProps {
  title: string;
  username: string;
  password: string;
  showActions: boolean;
  showError: boolean;
  errorMessage: string;
  isLoading: boolean;
  onCancel: ClickCallbackType;
  onLoginClicked: ClickCallbackType;
  onChangeUsername: ChangeCallbackType;
  onChangePassword: ChangeCallbackType;
  onCloseError: ClickCallbackType;
}

export function PureUserLogin({
  title,
  username,
  password,
  showActions,
  showError,
  errorMessage,
  isLoading,
  onCancel,
  onLoginClicked,
  onChangeUsername,
  onChangePassword,
  onCloseError,
}: PureUserLoginProps) {
  const { t } = useTranslation();
  const usernameRef = React.useCallback((node: HTMLDivElement) => {
    if (node !== null) {
      node.focus();
    }
  }, []);

  function onClose() {
    // Do nothing because we're not supposed to close on backdrop click
  }

  function handleKeyPress(event: React.KeyboardEvent) {
    if (event.key === 'Enter' && !isLoading) {
      onLoginClicked(event as any);
    }
  }

  return (
    <Box component="main">
      <ClusterDialog useCover onClose={onClose} aria-labelledby="userlogin-dialog-title">
        <DialogTitle id="userlogin-dialog-title">{title}</DialogTitle>
        <DialogContent>
          <DialogContentText>
            <Trans t={t}>Please enter your username and password.</Trans>
          </DialogContentText>
          <TextField
            margin="dense"
            id="username"
            label={t('Username')}
            type="text"
            size="small"
            variant="outlined"
            value={username}
            onChange={onChangeUsername}
            onKeyPress={handleKeyPress}
            fullWidth
            ref={usernameRef}
            disabled={isLoading}
            autoComplete="username"
          />
          <TextField
            margin="dense"
            id="password"
            label={t('Password')}
            type="password"
            size="small"
            variant="outlined"
            value={password}
            onChange={onChangePassword}
            onKeyPress={handleKeyPress}
            fullWidth
            disabled={isLoading}
            autoComplete="current-password"
            sx={{ mt: 2 }}
          />
        </DialogContent>
        <DialogActions>
          {showActions && (
            <Button onClick={onCancel} color="primary" disabled={isLoading}>
              {t('translation|Cancel')}
            </Button>
          )}
          <Button
            onClick={onLoginClicked}
            color="primary"
            variant="contained"
            disabled={isLoading || !username.trim() || !password.trim()}
          >
            {isLoading ? t('Authenticating...') : t('translation|Sign In')}
          </Button>
        </DialogActions>
      </ClusterDialog>
      <Snackbar
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'center',
        }}
        open={showError}
        autoHideDuration={5000}
        onClose={onCloseError}
        ContentProps={{
          'aria-describedby': 'message-id',
        }}
        message={<span id="message-id">{errorMessage}</span>}
      />
    </Box>
  );
}


