/*
 * Copyright 2025 The Kubernetes Authors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

import { Meta, StoryFn } from '@storybook/react';
import { TestContext } from '../../test';
import { PureUserLogin, PureUserLoginProps } from './UserLogin';

export default {
  title: 'UserLogin',
  component: PureUserLogin,
  argTypes: {
    onCancel: { action: 'cancel clicked' },
    onLoginClicked: { action: 'login clicked' },
    onChangeUsername: { action: 'username changed' },
    onChangePassword: { action: 'password changed' },
    onCloseError: { action: 'error closed' },
  },
  decorators: [
    Story => {
      return (
        <TestContext>
          <Story />
        </TestContext>
      );
    },
  ],
} as Meta;

const Template: StoryFn<PureUserLoginProps> = args => <PureUserLogin {...args} />;

export const Default = Template.bind({});
Default.args = {
  title: 'Authentication',
  username: '',
  password: '',
  showError: false,
  errorMessage: '',
  isLoading: false,
  showActions: false,
};

export const WithCredentials = Template.bind({});
WithCredentials.args = {
  title: 'Authentication',
  username: 'admin',
  password: 'password123',
  showError: false,
  errorMessage: '',
  isLoading: false,
  showActions: false,
};

export const ShowError = Template.bind({});
ShowError.args = {
  title: 'Authentication',
  username: '',
  password: '',
  showError: true,
  errorMessage: 'Invalid username or password',
  isLoading: false,
  showActions: false,
};

export const Loading = Template.bind({});
Loading.args = {
  title: 'Authentication',
  username: 'admin',
  password: 'password123',
  showError: false,
  errorMessage: '',
  isLoading: true,
  showActions: false,
};

export const ShowActions = Template.bind({});
ShowActions.args = {
  title: 'Authentication: my-cluster',
  username: '',
  password: '',
  showError: false,
  errorMessage: '',
  isLoading: false,
  showActions: true,
};
