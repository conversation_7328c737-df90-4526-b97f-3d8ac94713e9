{"name": "headlamp", "version": "0.1.0", "private": true, "productName": "Headlamp", "engines": {"npm": ">=10.0.0", "node": ">=20.11.1"}, "type": "module", "dependencies": {"@apidevtools/swagger-parser": "^10.0.3", "@dagrejs/dagre": "^1.1.2", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@headlamp-k8s/eslint-config": "^0.6.0", "@iconify/icons-mdi": "^1.2.9", "@iconify/react": "^3.2.1", "@monaco-editor/react": "^4.3.1", "@mui/icons-material": "^5.16.7", "@mui/lab": "^5.0.0-alpha.152", "@mui/material": "^5.15.14", "@mui/system": "^5.15.14", "@mui/x-date-pickers": "^7.15.0", "@mui/x-tree-view": "^6.17.0", "@reduxjs/toolkit": "^2.2.7", "@tanstack/react-query": "^5.51.24", "@tanstack/react-query-devtools": "^5.51.24", "@testing-library/dom": "^10.1.0", "@testing-library/react": "^16.0.0", "@types/humanize-duration": "^3.27.1", "@types/js-yaml": "^4.0.3", "@types/json-patch": "0.0.30", "@types/lodash": "4.17.7", "@types/node": "^20.12.11", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@types/react-redux": "^7.1.34", "@types/react-router": "^5.1.20", "@types/react-router-dom": "^5.3.1", "@types/react-window": "^1.8.8", "@types/semver": "^7.3.8", "@typescript-eslint/eslint-plugin": "^8.3.0", "@typescript-eslint/parser": "^8.3.0", "@vitejs/plugin-react": "^4.3.4", "@xterm/addon-fit": "^0.10.0", "@xterm/addon-search": "^0.15.0", "@xterm/xterm": "^5.5.0", "@xyflow/react": "^12.2.0", "base64-arraybuffer": "^1.0.2", "buffer": "^6.0.3", "console-browserify": "^1.2.0", "cronstrue": "^2.50.0", "cross-env": "^7.0.3", "crypto-browserify": "^3.12.0", "elkjs": "^0.9.3", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.24.2", "eslint-plugin-jsx-a11y": "^6.9.0", "eslint-plugin-react": "7.35.0", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-unused-imports": "^4.1.3", "fake-indexeddb": "^6.0.0", "fuse.js": "^7.0.0", "https-browserify": "^1.0.0", "humanize-duration": "^3.27.2", "i18next": "^23.15.1", "i18next-browser-languagedetector": "^8.0.0", "js-base64": "^3.7.2", "js-yaml": "^4.1.0", "jsonpath-plus": "^10.3.0", "lodash": "^4.17.21", "material-react-table": "^2.13.3", "monaco-editor": "^0.52.0", "notistack": "^3.0.2", "openapi-types": "^9.3.0", "process": "^0.11.10", "react": "^18.3.1", "react-dom": "^18.3.1", "react-dropzone": "^14.2.9", "react-hotkeys-hook": "^4.5.1", "react-i18next": "^15.0.2", "react-jwt": "^1.1.6", "react-markdown": "^9.0.1", "react-redux": "^9.1.2", "react-router": "^5.3.0", "react-router-dom": "^5.3.0", "react-window": "^1.8.11", "recharts": "^2.1.4", "semver": "^7.3.5", "shx": "^0.4.0", "simple-eval": "^2.0.0", "spacetime": "^7.4.0", "stream-browserify": "^3.0.0", "stream-http": "^3.2.0", "typescript": "5.6.2", "url": "^0.11.0", "util": "^0.12.4", "vite": "^6.3.5", "vite-plugin-node-polyfills": "^0.23.0", "vite-plugin-svgr": "^4.3.0", "web-worker": "^1.3.0"}, "overrides": {"axe-core": "npm:dry-uninstall", "domain-browser": "npm:dry-uninstall", "typescript": "5.6.2", "cheerio": "1.0.0-rc.12"}, "optionalDependencies": {"@rollup/rollup-darwin-arm64": "*", "@rollup/rollup-linux-x64-gnu": "*", "@rollup/rollup-win32-x64-msvc": "*"}, "scripts": {"prestart": "npm run make-version", "start": "cross-env REACT_APP_HEADLAMP_BACKEND_TOKEN=headlamp vite", "preview": "vite preview", "prebuild": "npm run make-version", "build": "cross-env PUBLIC_URL=./ NODE_OPTIONS=--max-old-space-size=8096 vite build && npx shx rm -f build/frontend/index.baseUrl.html", "pretest": "npm run make-version", "test": "vitest", "start-without-multiplexer": "cross-env REACT_APP_ENABLE_WEBSOCKET_MULTIPLEXER=false npm run start", "lint": "eslint --cache -c package.json --ext .js,.ts,.tsx src/ ../app/electron ../plugins/headlamp-plugin --ignore-pattern ../plugins/headlamp-plugin/template --ignore-pattern ../plugins/headlamp-plugin/lib/", "format": "prettier --config package.json --write --cache src ../app/electron ../app/tsconfig.json ../app/scripts ../plugins/headlamp-plugin/bin ../plugins/headlamp-plugin/config ../plugins/headlamp-plugin/template ../plugins/headlamp-plugin/test*.js ../plugins/headlamp-plugin/*.json ../plugins/headlamp-plugin/*.js", "format-check": "prettier --config package.json --check --cache src ../app/electron ../app/tsconfig.json ../app/scripts ../plugins/headlamp-plugin/bin ../plugins/headlamp-plugin/config ../plugins/headlamp-plugin/template ../plugins/headlamp-plugin/test*.js ../plugins/headlamp-plugin/*.json ../plugins/headlamp-plugin/*.js", "storybook": "storybook dev -p 6006", "build-typedoc": "typedoc", "build-storybook": "storybook build -o ../docs/development/storybook", "i18n": "i18next 'src/**/*.{ts,tsx}' -c ./src/i18n/i18next-parser.config.js", "tsc": "tsc", "make-version": "node ./make-env.js"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{js,jsx,ts,tsx}": ["eslint -c package.json --fix"], "src/**/*.{js,jsx,ts,tsx,json,css,scss,md}": ["prettier --config package.json --write"], "../app/**/*.{ts,tsx}": ["eslint -c package.json --fix"], "../app/**/*.{js,jsx,ts,tsx,json,css,scss,md}": ["prettier --config package.json --write"], "../plugins/headlamp-plugin/{bin,lib}/**/*.{ts,tsx}": ["eslint -c package.json --fix"], "../plugins/headlamp-plugin/{bin,lib}/**/*.{js,jsx,ts,tsx,json,css,scss,md}": ["prettier --config package.json --write"], "../plugins/examples/**/*.{ts,tsx}": ["eslint -c package.json --fix --resolve-plugins-relative-to ."], "../plugins/examples/**/*.{js,jsx,ts,tsx,json,css,scss,md}": ["prettier --config package.json --write"], "../e2e-tests/**/*.{js,jsx,ts,tsx,json,css,scss,md}": ["prettier --config package.json --write"], "../e2e-tests/**/*.{ts,tsx}": ["eslint -c package.json --fix --resolve-plugins-relative-to ."]}, "eslintConfig": {"plugins": ["license-header"], "extends": ["@headlamp-k8s", "prettier", "plugin:jsx-a11y/recommended"], "rules": {"license-header/header": ["error", ["/*", " * Copyright 2025 The Kubernetes Authors", " *", " * Licensed under the Apache License, Version 2.0 (the \"License\");", " * you may not use this file except in compliance with the License.", " * You may obtain a copy of the License at", " *", " * http://www.apache.org/licenses/LICENSE-2.0", " *", " * Unless required by applicable law or agreed to in writing, software", " * distributed under the License is distributed on an \"AS IS\" BASIS,", " * WITHOUT WAR<PERSON>NTIES OR CONDITIONS OF ANY KIND, either express or implied.", " * See the License for the specific language governing permissions and", " * limitations under the License.", " */"]]}}, "prettier": "@headlamp-k8s/eslint-config/prettier-config", "devDependencies": {"@storybook/addon-actions": "^8.5.6", "@storybook/addon-essentials": "^8.5.6", "@storybook/addon-interactions": "^8.5.6", "@storybook/addon-links": "^8.5.6", "@storybook/blocks": "^8.5.6", "@storybook/manager-api": "^8.5.6", "@storybook/node-logger": "^8.5.6", "@storybook/react-vite": "^8.5.6", "@storybook/test": "^8.5.6", "@storybook/theming": "^8.5.6", "@testing-library/jest-dom": "^6.4.8", "@testing-library/user-event": "^14.5.2", "@vitest/coverage-istanbul": "^3.0.5", "eslint-plugin-license-header": "^0.8.0", "husky": "^4.3.8", "i18next-parser": "^9.1.0", "jsdom": "^24.0.0", "lint-staged": "^10.5.4", "msw": "2.4.9", "msw-storybook-addon": "2.0.3", "nock": "^14.0.0-beta.14", "prettier": "^2.7.1", "resize-observer-polyfill": "^1.5.1", "storybook": "^8.5.6", "typedoc": "^0.26.5", "typedoc-plugin-markdown": "^4.2.3", "typedoc-plugin-rename-defaults": "^0.7.1", "vitest": "^3.0.5", "vitest-canvas-mock": "^0.3.3", "vitest-websocket-mock": "^0.4.0", "vm-browserify": "^1.1.2"}, "msw": {"workerDirectory": ["public"]}}