#!/bin/bash

# Start Headlamp with user authentication enabled

echo "Setting up Headlamp with user authentication..."

# Set environment variables for user authentication
export HEADLAMP_CONFIG_ENABLE_USER_AUTH=true
export HEADLAMP_CONFIG_USER_AUTH_CONFIG_FILE="$(pwd)/users.yaml"
export HEADLAMP_CONFIG_USER_AUTH_JWT_SECRET="your-super-secret-jwt-key-change-this-in-production"
export HEADLAMP_CONFIG_USER_AUTH_TOKEN_EXPIRATION="24h"

# Optional: Set other Headlamp configurations
export HEADLAMP_CONFIG_PORT=4466
export HEADLAMP_CONFIG_DEV=true

echo "User authentication configuration:"
echo "  Enabled: $HEADLAMP_CONFIG_ENABLE_USER_AUTH"
echo "  Config file: $HEADLAMP_CONFIG_USER_AUTH_CONFIG_FILE"
echo "  Token expiration: $HEADLAMP_CONFIG_USER_AUTH_TOKEN_EXPIRATION"
echo ""

echo "Test users available:"
echo "  admin / admin123 (cluster-admin)"
echo "  developer / dev123 (edit access)"
echo "  viewer / view123 (read-only)"
echo ""

echo "Make sure to apply the RBAC configuration first:"
echo "  kubectl apply -f headlamp-rbac.yaml"
echo ""

echo "Starting Headlamp server..."
echo "Access the application at: http://localhost:4466"
echo ""

# Start the headlamp server
# Note: Replace this with the actual path to your headlamp binary
/home/<USER>/workspace/headlamp/backend/headlamp-server
