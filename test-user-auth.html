<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test User Authentication</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], input[type="password"] {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <h1>Headlamp User Authentication Test</h1>
    
    <div class="info result">
        <h3>Test Users:</h3>
        <ul>
            <li><strong>admin</strong> / admin123 (cluster-admin privileges)</li>
            <li><strong>developer</strong> / dev123 (edit privileges)</li>
            <li><strong>viewer</strong> / view123 (read-only privileges)</li>
        </ul>
    </div>

    <form id="loginForm">
        <div class="form-group">
            <label for="username">Username:</label>
            <input type="text" id="username" name="username" required>
        </div>
        
        <div class="form-group">
            <label for="password">Password:</label>
            <input type="password" id="password" name="password" required>
        </div>
        
        <button type="submit" id="loginBtn">Login</button>
    </form>

    <div id="result" class="result" style="display: none;"></div>

    <h2>API Endpoints Test</h2>
    <button onclick="testAuthStatus()">Test Auth Status</button>
    <button onclick="testLogout()">Test Logout</button>

    <script>
        const API_BASE = window.location.origin;
        
        document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const loginBtn = document.getElementById('loginBtn');
            const resultDiv = document.getElementById('result');
            
            loginBtn.disabled = true;
            loginBtn.textContent = 'Logging in...';
            
            try {
                const response = await fetch(`${API_BASE}/api/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ username, password }),
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        <h3>Login Successful!</h3>
                        <p><strong>User:</strong> ${data.user.username}</p>
                        <p><strong>Service Account:</strong> ${data.user.serviceAccount}</p>
                        <p><strong>Namespace:</strong> ${data.user.namespace}</p>
                        <p><strong>Token expires at:</strong> ${data.expiresAt}</p>
                    `;
                    
                    // Store token for testing
                    localStorage.setItem('authToken', data.token);
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `
                        <h3>Login Failed</h3>
                        <p>${data.message || 'Authentication failed'}</p>
                    `;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    <h3>Error</h3>
                    <p>Network error: ${error.message}</p>
                `;
            }
            
            resultDiv.style.display = 'block';
            loginBtn.disabled = false;
            loginBtn.textContent = 'Login';
        });
        
        async function testAuthStatus() {
            const resultDiv = document.getElementById('result');
            
            try {
                const response = await fetch(`${API_BASE}/api/auth/status`);
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.className = 'result info';
                    resultDiv.innerHTML = `
                        <h3>Auth Status</h3>
                        <p><strong>Enabled:</strong> ${data.enabled}</p>
                        <p><strong>Authenticated:</strong> ${data.authenticated}</p>
                        ${data.user ? `
                            <p><strong>Current User:</strong> ${data.user.username}</p>
                            <p><strong>Service Account:</strong> ${data.user.serviceAccount}</p>
                        ` : ''}
                    `;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `<h3>Error</h3><p>${data.message || 'Failed to get auth status'}</p>`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `<h3>Error</h3><p>Network error: ${error.message}</p>`;
            }
            
            resultDiv.style.display = 'block';
        }
        
        async function testLogout() {
            const resultDiv = document.getElementById('result');
            
            try {
                const response = await fetch(`${API_BASE}/api/auth/logout`, {
                    method: 'POST',
                });
                
                if (response.ok) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = '<h3>Logout Successful</h3>';
                    localStorage.removeItem('authToken');
                } else {
                    const data = await response.json();
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `<h3>Logout Failed</h3><p>${data.message || 'Logout failed'}</p>`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `<h3>Error</h3><p>Network error: ${error.message}</p>`;
            }
            
            resultDiv.style.display = 'block';
        }
        
        // Test auth status on page load
        window.addEventListener('load', testAuthStatus);
    </script>
</body>
</html>
