{"name": "@headlamp-k8s/eslint-config", "version": "0.6.0", "description": "Lint rules for Headlamp TypeScript projects", "main": "index.js", "private": false, "files": ["prettier-config", "index.js", "LICENSE", "README.md"], "scripts": {"create": "node ./export.js > ./index.js && eslint ./index.js --fix", "lint": "eslint ./export.js prettier-config/index.js"}, "repository": {"type": "git", "url": "git+ssh://**************/headlamp-k8s/headlamp.git", "directory": "lint-config"}, "keywords": ["eslint", "eslintconfig", "prettierconfig", "headlamp"], "author": "Headlamp developers <<EMAIL>>", "license": "Apache-2.0", "bugs": {"url": "https://github.com/kubernetes-sigs/headlamp/issues"}, "homepage": "https://github.com/kubernetes-sigs/headlamp/blob/main/eslint-config/README.md", "peerDependencies": {"@typescript-eslint/eslint-plugin": "^8.3.0", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.24.2", "eslint-plugin-jsx-a11y": "^6.9.0", "eslint-plugin-react": "7.35.0", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-unused-imports": "^4.1.3"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^8.3.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.24.2", "eslint-plugin-jsx-a11y": "^6.9.0", "eslint-plugin-react": "7.35.0", "eslint-plugin-react-hooks": "^4.6.2", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-unused-imports": "^4.1.3", "yaml": "^2.5.0"}, "dependencies": {"@typescript-eslint/parser": "^8.3.0", "eslint": "^8.57.0", "typescript": "5.5.4"}}