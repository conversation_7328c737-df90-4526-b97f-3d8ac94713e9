# Headlamp 用户名密码认证系统

本文档说明如何使用新实现的用户名+密码认证系统，该系统将用户名密码映射到不同的ServiceAccount Token。

## 功能特性

- ✅ 用户名密码登录界面
- ✅ 用户名到ServiceAccount Token的映射
- ✅ JWT Token认证机制
- ✅ 与现有Headlamp认证系统集成
- ✅ 最小化代码修改，保持原有代码风格

## 实现的组件

### 前端组件
1. **UserLogin组件** (`frontend/src/components/account/UserLogin.tsx`)
   - 用户名密码输入界面
   - 错误处理和加载状态
   - 与现有UI风格一致

2. **用户认证API模块** (`frontend/src/lib/userAuth.ts`)
   - 登录API调用
   - 认证状态检查
   - Token刷新和登出

3. **路由集成** (`frontend/src/lib/router.tsx`)
   - 添加 `/user-login` 路由
   - 集成到现有路由系统

4. **AuthChooser更新** (`frontend/src/components/authchooser/index.tsx`)
   - 添加"用户名密码"登录按钮
   - 动态检测用户认证是否启用

### 后端集成
1. **配置选项** (`backend/pkg/config/config.go`)
   - `--enable-user-auth`: 启用用户认证
   - `--user-auth-config-file`: 用户配置文件路径
   - `--user-auth-jwt-secret`: JWT密钥
   - `--user-auth-token-expiration`: Token过期时间

2. **API端点** (`backend/cmd/headlamp.go`)
   - `POST /api/auth/login`: 用户登录
   - `POST /api/auth/logout`: 用户登出
   - `POST /api/auth/refresh`: Token刷新
   - `GET /api/auth/status`: 认证状态

## 使用方法

### 1. 准备ServiceAccount和RBAC

首先创建所需的ServiceAccount和RBAC配置：

```bash
kubectl apply -f headlamp-rbac.yaml
```

### 2. 配置用户文件

创建用户配置文件 `users.yaml`：

```yaml
users:
  - username: admin
    passwordHash: $2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/hL9B8L3Sm  # admin123
    serviceAccount: headlamp-admin
    namespace: kube-system
    enabled: true
    description: "System administrator"
    
  - username: developer
    passwordHash: $2a$12$9y8Nd2j3kL4mN5oP6qR7sT8uV9wX0yZ1aB2cD3eF4gH5iJ6kL7mN8o  # dev123
    serviceAccount: headlamp-developer
    namespace: default
    enabled: true
    description: "Developer with edit access"
```

### 3. 启动Headlamp服务器

使用提供的启动脚本：

```bash
chmod +x start-with-user-auth.sh
./start-with-user-auth.sh
```

或者手动启动：

```bash
export HEADLAMP_CONFIG_ENABLE_USER_AUTH=true
export HEADLAMP_CONFIG_USER_AUTH_CONFIG_FILE="$(pwd)/users.yaml"
export HEADLAMP_CONFIG_USER_AUTH_JWT_SECRET="your-super-secret-jwt-key"
export HEADLAMP_CONFIG_USER_AUTH_TOKEN_EXPIRATION="24h"

./headlamp-server
```

### 4. 访问应用

1. 打开浏览器访问 `http://localhost:4466`
2. 在认证选择页面，点击"Username & Password"按钮
3. 输入用户名和密码登录

## 测试用户

系统预配置了以下测试用户：

| 用户名 | 密码 | 权限 | ServiceAccount |
|--------|------|------|----------------|
| admin | admin123 | cluster-admin | headlamp-admin |
| developer | dev123 | edit | headlamp-developer |
| viewer | view123 | view | headlamp-viewer |

## 测试页面

打开 `test-user-auth.html` 文件可以测试认证API端点：

```bash
# 在浏览器中打开
open test-user-auth.html
```

## 技术实现细节

### 认证流程
1. 用户在前端输入用户名密码
2. 前端调用 `/api/auth/login` API
3. 后端验证用户凭据并生成JWT Token
4. JWT Token包含ServiceAccount信息
5. 前端存储Token并用于后续API调用

### 安全特性
- 密码使用bcrypt哈希存储
- JWT Token有过期时间
- 支持Token刷新机制
- 与Kubernetes RBAC集成

### 集成方式
- 复用现有的认证框架
- 保持与Token认证的兼容性
- 最小化对现有代码的修改
- 遵循Headlamp的代码风格

## 故障排除

### 常见问题

1. **用户认证按钮不显示**
   - 检查 `--enable-user-auth` 是否设置为true
   - 确认后端用户认证系统正常启动

2. **登录失败**
   - 检查用户配置文件路径是否正确
   - 验证密码哈希是否正确生成
   - 确认ServiceAccount是否存在

3. **Token过期**
   - 检查Token过期时间设置
   - 使用刷新Token功能

### 日志检查

查看Headlamp服务器日志：
- 用户认证启用/禁用状态
- 用户管理器初始化状态
- 认证路由注册状态

## 下一步

- [ ] 添加用户管理界面
- [ ] 支持用户权限动态配置
- [ ] 集成LDAP/AD认证
- [ ] 添加审计日志功能
